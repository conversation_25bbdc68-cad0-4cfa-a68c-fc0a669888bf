package ${packageName}.domain;

    #foreach ($import in $importList)
    import ${import};
    #end
import com.szz.common.annotation.Excel;
import com.baomidou.mybatisplus.annotation.*;
import lombok.*;
import com.szz.common.core.domain.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;


#if($table.crud || $table.sub)
#elseif($table.tree)
#end

/**
 * ${functionName}对象 ${tableName}
 *
 * <AUTHOR>
 * @date ${datetime}
 */
#if($table.crud || $table.sub)
    #set($Entity="BaseEntity")
#elseif($table.tree)
    #set($Entity="TreeEntity")
#end
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Schema(name = "${ClassName}对象", description = "${table.tableComment}")
@TableName("${tableName}")
public class ${ClassName} extends ${Entity}{
private static final long serialVersionUID=1L;

#foreach ($column in $columns)
    #if(!$table.isSuperColumn($column.javaField))
    /**
     * $column.columnComment
     */
    @Schema(description = "$column.columnComment")
        #if($column.javaField == "id")
        @TableId(value = "${column.columnName}", type = IdType.AUTO)
        #else
        @TableField("${column.columnName}")
        #end
        #if($column.list)
            #set($parentheseIndex=$column.columnComment.indexOf("（"))
            #if($parentheseIndex != -1)
                #set($comment=$column.columnComment.substring(0, $parentheseIndex))
            #else
                #set($comment=$column.columnComment)
            #end
            #if($parentheseIndex != -1)
            @Excel(name = "${comment}" , readConverterExp = "$column.readConverterExp()")
            #elseif($column.javaType == 'Date')
            @JsonFormat(pattern = "yyyy-MM-dd")
            @Excel(name = "${comment}" , width = 30, dateFormat = "yyyy-MM-dd")
            #else
            @Excel(name = "${comment}")
            #end
        #end
    private $column.javaType $column.javaField;

    #end
#end
#if($table.sub)

/**
 * $table.subTable.functionName信息
 */
private List<${subClassName}> ${subclassName}List;

#end

        }
