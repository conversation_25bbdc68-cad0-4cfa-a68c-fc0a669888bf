package ${packageName}.controller;

import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.v3.oas.annotations.*;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.web.bind.annotation.*;
import org.springframework.security.access.prepost.PreAuthorize;

import ${packageName}.domain.${ClassName};
import ${packageName}.service.I${ClassName}Service;
import com.szz.common.core.controller.BaseController;
import com.szz.common.core.domain.AjaxResult;
import com.szz.common.utils.poi.ExcelUtil;
import com.szz.common.enums.BusinessType;
import com.szz.common.annotation.Log;
#if($table.crud || $table.sub)
import com.szz.common.core.page.TableDataInfo;
#elseif($table.tree)
#end
import com.szz.common.annotation.RateLimiter;
import com.szz.common.enums.LimitType;

/**
 * ${functionName}Controller
 *
 * <AUTHOR>
 * @date ${datetime}
 */
@RestController
@RequestMapping("/${moduleName}/${businessName}")
@Tag(name = "${functionName}管理", description = "${functionName}管理")
public class ${ClassName}Controller extends BaseController {
    @Autowired
    private I${ClassName}Service ${className}Service;

/**
 * 查询${functionName}列表
 */
    @GetMapping("/list")
    @Operation(summary = "查询${functionName}列表")
    @RateLimiter(time = 2, count = 1, limitType = LimitType.IP, skipRoles = {"admin"})
    #if($table.crud || $table.sub)
    public TableDataInfo list(${ClassName} ${className})
    {
        startPage();
        List<${ClassName}> list = ${className}Service.select${ClassName}List(${className});
        return getDataTable(list);
    }
    #elseif($table.tree)
        public AjaxResult list(${ClassName} ${className})
        {
            List<${ClassName}> list = ${className}Service.select${ClassName}List(${className});
            return success(list);
        }
    #end

    /**
     * 导出${functionName}列表
     */
    @Log(title = "${functionName}", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @Operation(summary = "导出${functionName}列表")
    @RateLimiter(time = 5, count = 1, limitType = LimitType.IP, skipRoles = {"admin"})
    public void export(HttpServletResponse response, ${ClassName} ${className})
    {
        List<${ClassName}> list = ${className}Service.select${ClassName}List(${className});
        ExcelUtil<${ClassName}> util = new ExcelUtil<${ClassName}>(${ClassName}.class);
        util.exportExcel(response, list, "${functionName}数据");
    }

    /**
     * 获取${functionName}详细信息
     */
    @GetMapping(value = "/{${pkColumn.javaField}}")
    @Operation(summary = "获取${functionName}详细信息")
    @RateLimiter(time = 2, count = 1, limitType = LimitType.IP, skipRoles = {"admin"})
    public AjaxResult getInfo(@PathVariable(value = "${pkColumn.javaField}") ${pkColumn.javaType} ${pkColumn.javaField})
    {
        return success(${className}Service.select${ClassName}By${pkColumn.capJavaField}(${pkColumn.javaField}));
    }

    /**
     * 新增${functionName}
     */
    @Log(title = "${functionName}", businessType = BusinessType.INSERT)
    @PostMapping()
    @Operation(summary = "新增${functionName}")
    @RateLimiter(time = 5, count = 1, limitType = LimitType.IP, skipRoles = {"admin"})
    public AjaxResult add(@RequestBody ${ClassName} ${className})
    {
        return toAjax(${className}Service.insert${ClassName}(${className}));
    }

    /**
     * 修改${functionName}
     */
    @Log(title = "${functionName}", businessType = BusinessType.UPDATE)
    @PutMapping()
    @Operation(summary = "修改${functionName}")
    @RateLimiter(time = 5, count = 1, limitType = LimitType.IP, skipRoles = {"admin"})
    public AjaxResult edit(@RequestBody ${ClassName} ${className})
    {
        return toAjax(${className}Service.update${ClassName}(${className}));
    }

    /**
     * 删除${functionName}
     */
    @Log(title = "${functionName}", businessType = BusinessType.DELETE)
    @DeleteMapping("/{${pkColumn.javaField}s}")
    @Operation(summary = "删除${functionName}")
    @RateLimiter(time = 5, count = 1, limitType = LimitType.IP, skipRoles = {"admin"})
    public AjaxResult remove(@PathVariable(value = "${pkColumn.javaField}s") ${pkColumn.javaType}[] ${pkColumn.javaField}s)
    {
        return toAjax(${className}Service.delete${ClassName}By${pkColumn.capJavaField}s(${pkColumn.javaField}s));
    }
}
