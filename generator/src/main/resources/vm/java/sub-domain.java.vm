package ${packageName}.domain;

    #foreach ($import in $subImportList)
    import ${import};
    #end

import com.szz.common.annotation.Excel;
import com.szz.common.core.domain.BaseEntity;
import lombok.*;


/**
 * ${subTable.functionName}对象 ${subTableName}
 *
 * <AUTHOR>
 * @date ${datetime}
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder

public class ${subClassName} extends BaseEntity{
private static final long serialVersionUID = 1L;

#foreach ($column in $subTable.columns)
    #if(!$table.isSuperColumn($column.javaField))
    /** $column.columnComment */
        #if($column.list)
            #set($parentheseIndex=$column.columnComment.indexOf("（"))
            #if($parentheseIndex != -1)
                #set($comment=$column.columnComment.substring(0, $parentheseIndex))
            #else
                #set($comment=$column.columnComment)
            #end
            #if($parentheseIndex != -1)
            @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
            #elseif($column.javaType == 'Date')
            @JsonFormat(pattern = "yyyy-MM-dd")
            @Excel(name = "${comment}", width = 30, dateFormat = "yyyy-MM-dd")
            #else
            @Excel(name = "${comment}")
            #end
        #end
    private $column.javaType $column.javaField;

    #end
#end

}
