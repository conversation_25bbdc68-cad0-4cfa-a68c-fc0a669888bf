package com.szz.framework.web.advice;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.szz.common.annotation.Encrypt;
import com.szz.common.utils.crypto.AesCrypto;
import org.springframework.core.MethodParameter;
import org.springframework.http.MediaType;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;

import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

/**
 * 出参加密：当 Controller 方法标注 @Encrypt 时，将返回结果序列化为 JSON 后整体加密，
 * 包裹为 {"data":"Base64(SM4(cipher))"}
 */
@Component
@ControllerAdvice
public class EncryptResponseBodyAdvice implements ResponseBodyAdvice<Object> {

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public boolean supports(MethodParameter returnType, Class converterType) {
        return returnType.getMethod() != null && returnType.getMethod().isAnnotationPresent(Encrypt.class);
    }

    @Override
    public Object beforeBodyWrite(Object body, MethodParameter returnType, MediaType selectedContentType,
                                  Class selectedConverterType, ServerHttpRequest request, ServerHttpResponse response) {
        try {
            // 将原始返回体转为 JSON 文本
            String json = objectMapper.writeValueAsString(body);
            String cipher = AesCrypto.encrypt(json);
            Map<String, String> wrapped = new HashMap<>();
            wrapped.put("data", cipher);
            response.getHeaders().setContentType(MediaType.APPLICATION_JSON);
            response.getHeaders().set("Content-Encoding", "sm4-cbc");
            return wrapped;
        } catch (Exception e) {
            // 出现异常则透传原始数据，避免接口不可用
            return body;
        }
    }
}

