package com.szz.framework.web.advice;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.szz.common.annotation.Encrypt;
import com.szz.common.utils.crypto.AesCrypto;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.MethodParameter;
import org.springframework.http.HttpInputMessage;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.RequestBodyAdvice;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Type;
import java.nio.charset.StandardCharsets;
import java.util.Map;

/**
 * 入参解密：当 Controller 方法标注 @Encrypt 时，自动将请求体进行解密并还原 JSON。
 * 前端需将原始 JSON 文本加密后放入字段: {"data":"Base64(SM4(cipher))"}
 */
@Component
@ControllerAdvice
public class EncryptRequestBodyAdvice implements RequestBodyAdvice {

    @Autowired(required = false)
    private HttpServletRequest request;

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public boolean supports(MethodParameter methodParameter, Type targetType, Class<? extends HttpMessageConverter<?>> converterType) {
        return methodParameter.getMethod() != null && methodParameter.getMethod().isAnnotationPresent(Encrypt.class);
    }

    @Override
    public HttpInputMessage beforeBodyRead(HttpInputMessage inputMessage, MethodParameter parameter, Type targetType,
                                           Class<? extends HttpMessageConverter<?>> converterType) throws IOException {
        // 读取原始请求体
        String raw = new String(inputMessage.getBody().readAllBytes(), StandardCharsets.UTF_8);
        if (raw == null || raw.isBlank()) {
            return inputMessage;
        }
        // 约定请求体为 {"data":"Base64(SM4(cipher))"}
        String decryptedJson = raw;
        try {
            Map<?,?> map = objectMapper.readValue(raw, Map.class);
            Object data = map.get("data");
            if (data instanceof String ds && !ds.isBlank()) {
                decryptedJson = AesCrypto.decrypt(ds);
            }
        } catch (Exception ignore) {
            // 如果不是 JSON 或没有 data 字段，则按明文透传
        }
        final byte[] bytes = decryptedJson.getBytes(StandardCharsets.UTF_8);
        return new HttpInputMessage() {
            @Override
            public InputStream getBody() {
                return new ByteArrayInputStream(bytes);
            }
            @Override
            public org.springframework.http.HttpHeaders getHeaders() {
                return inputMessage.getHeaders();
            }
        };
    }

    @Override
    public Object afterBodyRead(Object body, HttpInputMessage inputMessage, MethodParameter parameter, Type targetType,
                                Class<? extends HttpMessageConverter<?>> converterType) {
        return body;
    }

    @Override
    public Object handleEmptyBody(Object body, HttpInputMessage inputMessage, MethodParameter parameter, Type targetType,
                                  Class<? extends HttpMessageConverter<?>> converterType) {
        return body;
    }
}

