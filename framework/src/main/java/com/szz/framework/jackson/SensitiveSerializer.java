package com.szz.framework.jackson;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.BeanDescription;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializationConfig;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.databind.ser.BeanPropertyWriter;
import com.fasterxml.jackson.databind.ser.BeanSerializerModifier;
import com.szz.common.annotation.Sensitive;
import com.szz.common.enums.SensitiveType;
import com.szz.common.utils.DesensitizeUtils;

import java.io.IOException;
import java.util.List;

/**
 * 针对标注 @Sensitive 的 String 字段进行脱敏输出。
 */
public class SensitiveSerializer extends JsonSerializer<String> {

	private final SensitiveType type;
	private final int prefix;
	private final int suffix;
	private final char mask;

	public SensitiveSerializer(SensitiveType type, int prefix, int suffix, char mask) {
		this.type = type;
		this.prefix = prefix;
		this.suffix = suffix;
		this.mask = mask;
	}

	@Override
	public void serialize(String value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
		gen.writeString(DesensitizeUtils.mask(value, type, prefix, suffix, mask));
	}

	/**
	 * 提供给 ObjectMapper 的模块，扫描字段并替换带注解的 Writer。
	 */
	public static class SensitiveModule extends SimpleModule {
		@Override
		public void setupModule(SetupContext context) {
			context.addBeanSerializerModifier(new BeanSerializerModifier() {
				@Override
				public List<BeanPropertyWriter> changeProperties(SerializationConfig config, BeanDescription beanDesc, List<BeanPropertyWriter> beanProperties) {
					for (int i = 0; i < beanProperties.size(); i++) {
						BeanPropertyWriter writer = beanProperties.get(i);
						if (writer.getMember() != null && writer.getMember().hasAnnotation(Sensitive.class)) {
							Sensitive anno = writer.getMember().getAnnotation(Sensitive.class);
							JavaType type = writer.getType();
							if (type != null && type.getRawClass() == String.class) {
								JsonSerializer<Object> ser = (JsonSerializer) new SensitiveSerializer(
										anno.type(), anno.prefix(), anno.suffix(), anno.mask());
								writer.assignSerializer(ser);
							}
						}
					}
					return beanProperties;
				}
			});
		}
	}
}


