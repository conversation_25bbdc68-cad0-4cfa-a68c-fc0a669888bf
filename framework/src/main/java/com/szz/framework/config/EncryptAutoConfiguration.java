package com.szz.framework.config;

import com.szz.common.utils.crypto.AesCrypto;
import jakarta.annotation.PostConstruct;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;

/**
 * 将 yml 中的加密配置注入到加解密工具中。
 */
@Configuration
public class EncryptAutoConfiguration {

    @Autowired(required = false)
    private EncryptProperties encryptProperties;

    @PostConstruct
    public void init() {
        if (encryptProperties != null) {
            AesCrypto.configure(encryptProperties.getKey(), encryptProperties.getIv());
        }
    }
}


