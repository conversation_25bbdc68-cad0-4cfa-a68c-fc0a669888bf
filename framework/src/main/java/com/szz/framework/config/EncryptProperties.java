package com.szz.framework.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 加解密相关配置，从 application.yml 读取。
 */
@Component
@ConfigurationProperties(prefix = "encrypt")
public class EncryptProperties {
    /**
     * SM4 密钥（16 字节）。
     */
    private String sm4Key;

    /**
     * SM4 偏移量 IV（16 字节）。
     */
    private String sm4Iv;

    /**
     * 兼容旧配置：AES 密钥（已用于 SM4，建议迁移到 sm4-key）。
     */
    @Deprecated
    private String aesKey;

    /**
     * 兼容旧配置：AES 偏移量 IV（已用于 SM4，建议迁移到 sm4-iv）。
     */
    @Deprecated
    private String aesIv;

    /**
     * 获取生效的密钥：优先返回 sm4Key；若为空则回退到 aesKey。
     * @return 16 字节密钥
     */
    public String getKey() {
        return (sm4Key != null && !sm4Key.isBlank()) ? sm4Key : aesKey;
    }

    /**
     * 获取生效的 IV：优先返回 sm4Iv；若为空则回退到 aesIv。
     * @return 16 字节 IV
     */
    public String getIv() {
        return (sm4Iv != null && !sm4Iv.isBlank()) ? sm4Iv : aesIv;
    }

    public String getSm4Key() {
        return sm4Key;
    }

    public void setSm4Key(String sm4Key) {
        this.sm4Key = sm4Key;
    }

    public String getSm4Iv() {
        return sm4Iv;
    }

    public void setSm4Iv(String sm4Iv) {
        this.sm4Iv = sm4Iv;
    }

    public String getAesKey() {
        return aesKey;
    }

    public void setAesKey(String aesKey) {
        this.aesKey = aesKey;
    }

    public String getAesIv() {
        return aesIv;
    }

    public void setAesIv(String aesIv) {
        this.aesIv = aesIv;
    }
}


