package com.szz.framework.aspectj;

import java.lang.reflect.Method;
import java.time.Duration;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.DefaultParameterNameDiscoverer;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import jakarta.servlet.http.HttpServletRequest;
import com.szz.common.annotation.Idempotent;
import com.szz.common.exception.ServiceException;
import com.szz.common.utils.StringUtils;

@Aspect
@Component
public class IdempotentAspect {

    private final StringRedisTemplate redis;
    private final ExpressionParser parser = new SpelExpressionParser();
    private final DefaultParameterNameDiscoverer nameDiscoverer = new DefaultParameterNameDiscoverer();

    @Autowired
    public IdempotentAspect(StringRedisTemplate redis) {
        this.redis = redis;
    }

    @Around("@annotation(idem)")
    public Object around(ProceedingJoinPoint pjp, Idempotent idem) throws Throwable {
        String key = resolveKey(pjp, idem);
        if (StringUtils.isEmpty(key)) {
            throw new ServiceException("幂等键为空，拒绝受理");
        }
        String redisKey = "idemp:" + key;
        Boolean ok = redis.opsForValue().setIfAbsent(redisKey, "1", Duration.ofSeconds(idem.windowSeconds()));
        if (Boolean.FALSE.equals(ok)) {
            throw new ServiceException(idem.message());
        }
        return pjp.proceed();
    }

    private String resolveKey(ProceedingJoinPoint pjp, Idempotent idem) {
        // 1) SpEL 优先
        if (StringUtils.isNotEmpty(idem.keySpEL())) {
            MethodSignature ms = (MethodSignature) pjp.getSignature();
            Method method = ms.getMethod();
            String[] paramNames = nameDiscoverer.getParameterNames(method);
            Object[] args = pjp.getArgs();
            EvaluationContext context = new StandardEvaluationContext();
            if (paramNames != null) {
                for (int i = 0; i < paramNames.length; i++) {
                    context.setVariable(paramNames[i], args[i]);
                }
            }
            Expression expr = parser.parseExpression(idem.keySpEL());
            Object val = expr.getValue(context);
            return val == null ? null : val.toString();
        }
        // 2) Header: Idempotency-Key
        ServletRequestAttributes attrs = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attrs != null) {
            HttpServletRequest req = attrs.getRequest();
            String header = req.getHeader("Idempotency-Key");
            if (StringUtils.isNotEmpty(header)) {
                return header;
            }
        }
        return null;
    }
}


