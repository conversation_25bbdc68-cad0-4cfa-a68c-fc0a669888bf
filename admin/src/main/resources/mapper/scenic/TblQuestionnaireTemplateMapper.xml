<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.szz.scenic.mapper.TblQuestionnaireTemplateMapper">
    
    <resultMap type="TblQuestionnaireTemplate" id="TblQuestionnaireTemplateResult">
        <result property="templateId"    column="template_id"    />
        <result property="templateName"    column="template_name"    />
        <result property="description"    column="description"    />
        <result property="structureJson"    column="structure_json"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectTblQuestionnaireTemplateVo">
        select template_id, template_name, description, structure_json, create_by, create_time, update_by, update_time, remark from tbl_questionnaire_template
    </sql>

    <select id="selectTblQuestionnaireTemplateList" parameterType="TblQuestionnaireTemplate" resultMap="TblQuestionnaireTemplateResult">
        <include refid="selectTblQuestionnaireTemplateVo"/>
        <where>  
            <if test="templateName != null  and templateName != ''"> and template_name like concat('%', #{templateName}, '%')</if>
            <if test="description != null  and description != ''"> and description = #{description}</if>
            <if test="structureJson != null  and structureJson != ''"> and structure_json = #{structureJson}</if>
        </where>
    </select>
    
    <select id="selectTblQuestionnaireTemplateByTemplateId" parameterType="Long" resultMap="TblQuestionnaireTemplateResult">
        <include refid="selectTblQuestionnaireTemplateVo"/>
        where template_id = #{templateId}
    </select>
        
    <insert id="insertTblQuestionnaireTemplate" parameterType="TblQuestionnaireTemplate" useGeneratedKeys="true" keyProperty="templateId">
        insert into tbl_questionnaire_template
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="templateName != null and templateName != ''">template_name,</if>
            <if test="description != null">description,</if>
            <if test="structureJson != null and structureJson != ''">structure_json,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="templateName != null and templateName != ''">#{templateName},</if>
            <if test="description != null">#{description},</if>
            <if test="structureJson != null and structureJson != ''">#{structureJson},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateTblQuestionnaireTemplate" parameterType="TblQuestionnaireTemplate">
        update tbl_questionnaire_template
        <trim prefix="SET" suffixOverrides=",">
            <if test="templateName != null and templateName != ''">template_name = #{templateName},</if>
            <if test="description != null">description = #{description},</if>
            <if test="structureJson != null and structureJson != ''">structure_json = #{structureJson},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where template_id = #{templateId}
    </update>

    <delete id="deleteTblQuestionnaireTemplateByTemplateId" parameterType="Long">
        delete from tbl_questionnaire_template where template_id = #{templateId}
    </delete>

    <delete id="deleteTblQuestionnaireTemplateByTemplateIds" parameterType="String">
        delete from tbl_questionnaire_template where template_id in 
        <foreach item="templateId" collection="array" open="(" separator="," close=")">
            #{templateId}
        </foreach>
    </delete>
</mapper>