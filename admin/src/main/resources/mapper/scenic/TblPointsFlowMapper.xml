<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.szz.scenic.mapper.TblPointsFlowMapper">
    
    <resultMap type="TblPointsFlow" id="TblPointsFlowResult">
        <result property="flowId"    column="flow_id"    />
        <result property="userId"    column="user_id"    />
        <result property="flowType"    column="flow_type"    />
        <result property="sourceType"    column="source_type"    />
        <result property="sourceDesc"    column="source_desc"    />
        <result property="pointsAmount"    column="points_amount"    />
        <result property="beforePoints"    column="before_points"    />
        <result property="afterPoints"    column="after_points"    />
        <result property="createTime"    column="create_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectTblPointsFlowVo">
        select flow_id, user_id, flow_type, source_type, source_desc, points_amount, before_points, after_points, create_time, remark from tbl_points_flow
    </sql>

    <select id="selectTblPointsFlowList" parameterType="TblPointsFlow" resultMap="TblPointsFlowResult">
        <include refid="selectTblPointsFlowVo"/>
        <where>  
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="flowType != null  and flowType != ''"> and flow_type = #{flowType}</if>
            <if test="sourceType != null  and sourceType != ''"> and source_type = #{sourceType}</if>
            <if test="sourceDesc != null  and sourceDesc != ''"> and source_desc = #{sourceDesc}</if>
            <if test="pointsAmount != null "> and points_amount = #{pointsAmount}</if>
            <if test="beforePoints != null "> and before_points = #{beforePoints}</if>
            <if test="afterPoints != null "> and after_points = #{afterPoints}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectTblPointsFlowByFlowId" parameterType="Long" resultMap="TblPointsFlowResult">
        <include refid="selectTblPointsFlowVo"/>
        where flow_id = #{flowId}
    </select>
        
    <insert id="insertTblPointsFlow" parameterType="TblPointsFlow" useGeneratedKeys="true" keyProperty="flowId">
        insert into tbl_points_flow
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="flowType != null">flow_type,</if>
            <if test="sourceType != null">source_type,</if>
            <if test="sourceDesc != null">source_desc,</if>
            <if test="pointsAmount != null">points_amount,</if>
            <if test="beforePoints != null">before_points,</if>
            <if test="afterPoints != null">after_points,</if>
            <if test="createTime != null">create_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="flowType != null">#{flowType},</if>
            <if test="sourceType != null">#{sourceType},</if>
            <if test="sourceDesc != null">#{sourceDesc},</if>
            <if test="pointsAmount != null">#{pointsAmount},</if>
            <if test="beforePoints != null">#{beforePoints},</if>
            <if test="afterPoints != null">#{afterPoints},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateTblPointsFlow" parameterType="TblPointsFlow">
        update tbl_points_flow
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="flowType != null">flow_type = #{flowType},</if>
            <if test="sourceType != null">source_type = #{sourceType},</if>
            <if test="sourceDesc != null">source_desc = #{sourceDesc},</if>
            <if test="pointsAmount != null">points_amount = #{pointsAmount},</if>
            <if test="beforePoints != null">before_points = #{beforePoints},</if>
            <if test="afterPoints != null">after_points = #{afterPoints},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where flow_id = #{flowId}
    </update>

    <delete id="deleteTblPointsFlowByFlowId" parameterType="Long">
        delete from tbl_points_flow where flow_id = #{flowId}
    </delete>

    <delete id="deleteTblPointsFlowByFlowIds" parameterType="String">
        delete from tbl_points_flow where flow_id in 
        <foreach item="flowId" collection="array" open="(" separator="," close=")">
            #{flowId}
        </foreach>
    </delete>
</mapper>