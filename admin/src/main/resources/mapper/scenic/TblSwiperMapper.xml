<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.szz.scenic.mapper.TblSwiperMapper">
    
    <resultMap type="TblSwiper" id="TblSwiperResult">
        <result property="id"    column="id"    />
        <result property="pic"    column="pic"    />
        <result property="navTo"    column="nav_to"    />
        <result property="isExternal"    column="is_external"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectTblSwiperVo">
        select id, pic, nav_to, is_external, create_by, create_time, update_by, update_time, remark from tbl_swiper
    </sql>

    <select id="selectTblSwiperList" parameterType="TblSwiper" resultMap="TblSwiperResult">
        <include refid="selectTblSwiperVo"/>
        <where>  
            <if test="pic != null  and pic != ''"> and pic = #{pic}</if>
            <if test="navTo != null  and navTo != ''"> and nav_to = #{navTo}</if>
            <if test="isExternal != null  and isExternal != ''"> and is_external = #{isExternal}</if>
        </where>
    </select>
    
    <select id="selectTblSwiperById" parameterType="Long" resultMap="TblSwiperResult">
        <include refid="selectTblSwiperVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertTblSwiper" parameterType="TblSwiper" useGeneratedKeys="true" keyProperty="id">
        insert into tbl_swiper
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="pic != null">pic,</if>
            <if test="navTo != null">nav_to,</if>
            <if test="isExternal != null">is_external,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="pic != null">#{pic},</if>
            <if test="navTo != null">#{navTo},</if>
            <if test="isExternal != null">#{isExternal},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateTblSwiper" parameterType="TblSwiper">
        update tbl_swiper
        <trim prefix="SET" suffixOverrides=",">
            <if test="pic != null">pic = #{pic},</if>
            <if test="navTo != null">nav_to = #{navTo},</if>
            <if test="isExternal != null">is_external = #{isExternal},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTblSwiperById" parameterType="Long">
        delete from tbl_swiper where id = #{id}
    </delete>

    <delete id="deleteTblSwiperByIds" parameterType="String">
        delete from tbl_swiper where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>