<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.szz.scenic.mapper.TblOrderMapper">
    
    <resultMap type="TblOrder" id="TblOrderResult">
        <result property="orderId"    column="order_id"    />
        <result property="orderSn"    column="order_sn"    />
        <result property="userId"    column="user_id"    />
        <result property="ticketId"    column="ticket_id"    />
        <result property="ticketSkuJson"    column="ticket_sku_json"    />
        <result property="quantity"    column="quantity"    />
        <result property="totalAmount"    column="total_amount"    />
        <result property="payStatus"    column="pay_status"    />
        <result property="orderStatus"    column="order_status"    />
        <result property="payMethod"    column="pay_method"    />
        <result property="transactionId"    column="transaction_id"    />
        <result property="prepayId"    column="prepay_id"    />
        <result property="payTime"    column="pay_time"    />
        <result property="temId" column="tem_id" />
        <result property="questionnaireResult" column="questionnaire_result" />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectTblOrderVo">
        select order_id, order_sn, tem_id, questionnaire_result, user_id, ticket_id, ticket_sku_json, quantity, total_amount, pay_status, order_status, pay_method, transaction_id, prepay_id, pay_time, create_by, create_time, update_by, update_time from tbl_order
    </sql>

    <select id="selectTblOrderList" parameterType="TblOrder" resultMap="TblOrderResult">
        <include refid="selectTblOrderVo"/>
        <where>  
            <if test="orderSn != null  and orderSn != ''"> and order_sn = #{orderSn}</if>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="ticketId != null "> and ticket_id = #{ticketId}</if>
            <if test="ticketSkuJson != null  and ticketSkuJson != ''"> and ticket_sku_json = #{ticketSkuJson}</if>
            <if test="quantity != null "> and quantity = #{quantity}</if>
            <if test="totalAmount != null "> and total_amount = #{totalAmount}</if>
            <if test="payStatus != null  and payStatus != ''"> and pay_status = #{payStatus}</if>
            <if test="orderStatus != null  and orderStatus != ''"> and order_status = #{orderStatus}</if>
            <if test="payMethod != null  and payMethod != ''"> and pay_method = #{payMethod}</if>
            <if test="transactionId != null  and transactionId != ''"> and transaction_id = #{transactionId}</if>
            <if test="prepayId != null  and prepayId != ''"> and prepay_id = #{prepayId}</if>
            <if test="payTime != null "> and pay_time = #{payTime}</if>
        </where>
    </select>
    
    <select id="selectTblOrderByOrderId" parameterType="Long" resultMap="TblOrderResult">
        <include refid="selectTblOrderVo"/>
        where order_id = #{orderId}
    </select>
        
    <insert id="insertTblOrder" parameterType="TblOrder" useGeneratedKeys="true" keyProperty="orderId">
        insert into tbl_order
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orderSn != null and orderSn != ''">order_sn,</if>
            <if test="temId != null">tem_id,</if>
            <if test="questionnaireResult != null">questionnaire_result,</if>
            <if test="userId != null">user_id,</if>
            <if test="ticketId != null">ticket_id,</if>
            <if test="ticketSkuJson != null">ticket_sku_json,</if>
            <if test="quantity != null">quantity,</if>
            <if test="totalAmount != null">total_amount,</if>
            <if test="payStatus != null">pay_status,</if>
            <if test="orderStatus != null">order_status,</if>
            <if test="payMethod != null">pay_method,</if>
            <if test="transactionId != null">transaction_id,</if>
            <if test="prepayId != null">prepay_id,</if>
            <if test="payTime != null">pay_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orderSn != null and orderSn != ''">#{orderSn},</if>
            <if test="temId != null">#{temId},</if>
            <if test="questionnaireResult != null">#{questionnaireResult},</if>
            <if test="userId != null">#{userId},</if>
            <if test="ticketId != null">#{ticketId},</if>
            <if test="ticketSkuJson != null">#{ticketSkuJson},</if>
            <if test="quantity != null">#{quantity},</if>
            <if test="totalAmount != null">#{totalAmount},</if>
            <if test="payStatus != null">#{payStatus},</if>
            <if test="orderStatus != null">#{orderStatus},</if>
            <if test="payMethod != null">#{payMethod},</if>
            <if test="transactionId != null">#{transactionId},</if>
            <if test="prepayId != null">#{prepayId},</if>
            <if test="payTime != null">#{payTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateTblOrder" parameterType="TblOrder">
        update tbl_order
        <trim prefix="SET" suffixOverrides=",">
            <if test="orderSn != null and orderSn != ''">order_sn = #{orderSn},</if>
            <if test="temId != null">tem_id = #{temId},</if>
            <if test="questionnaireResult != null">questionnaire_result = #{questionnaireResult},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="ticketId != null">ticket_id = #{ticketId},</if>
            <if test="ticketSkuJson != null">ticket_sku_json = #{ticketSkuJson},</if>
            <if test="quantity != null">quantity = #{quantity},</if>
            <if test="totalAmount != null">total_amount = #{totalAmount},</if>
            <if test="payStatus != null">pay_status = #{payStatus},</if>
            <if test="orderStatus != null">order_status = #{orderStatus},</if>
            <if test="payMethod != null">pay_method = #{payMethod},</if>
            <if test="transactionId != null">transaction_id = #{transactionId},</if>
            <if test="prepayId != null">prepay_id = #{prepayId},</if>
            <if test="payTime != null">pay_time = #{payTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where order_id = #{orderId}
    </update>

    <delete id="deleteTblOrderByOrderId" parameterType="Long">
        delete from tbl_order where order_id = #{orderId}
    </delete>

    <delete id="deleteTblOrderByOrderIds" parameterType="String">
        delete from tbl_order where order_id in 
        <foreach item="orderId" collection="array" open="(" separator="," close=")">
            #{orderId}
        </foreach>
    </delete>
</mapper>