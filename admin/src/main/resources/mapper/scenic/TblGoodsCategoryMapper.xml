<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.szz.scenic.mapper.TblGoodsCategoryMapper">
    
    <resultMap type="TblGoodsCategory" id="TblGoodsCategoryResult">
        <result property="categoryId"    column="category_id"    />
        <result property="categoryName"    column="category_name"    />
        <result property="icon"    column="icon"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectTblGoodsCategoryVo">
        select category_id, category_name, icon, create_by, create_time, update_by, update_time from tbl_goods_category
    </sql>

    <select id="selectTblGoodsCategoryList" parameterType="TblGoodsCategory" resultMap="TblGoodsCategoryResult">
        <include refid="selectTblGoodsCategoryVo"/>
        <where>  
            <if test="categoryName != null  and categoryName != ''"> and category_name like concat('%', #{categoryName}, '%')</if>
            <if test="icon != null  and icon != ''"> and icon = #{icon}</if>
        </where>
    </select>
    
    <select id="selectTblGoodsCategoryByCategoryId" parameterType="Long" resultMap="TblGoodsCategoryResult">
        <include refid="selectTblGoodsCategoryVo"/>
        where category_id = #{categoryId}
    </select>
        
    <insert id="insertTblGoodsCategory" parameterType="TblGoodsCategory" useGeneratedKeys="true" keyProperty="categoryId">
        insert into tbl_goods_category
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="categoryName != null and categoryName != ''">category_name,</if>
            <if test="icon != null">icon,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="categoryName != null and categoryName != ''">#{categoryName},</if>
            <if test="icon != null">#{icon},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateTblGoodsCategory" parameterType="TblGoodsCategory">
        update tbl_goods_category
        <trim prefix="SET" suffixOverrides=",">
            <if test="categoryName != null and categoryName != ''">category_name = #{categoryName},</if>
            <if test="icon != null">icon = #{icon},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where category_id = #{categoryId}
    </update>

    <delete id="deleteTblGoodsCategoryByCategoryId" parameterType="Long">
        delete from tbl_goods_category where category_id = #{categoryId}
    </delete>

    <delete id="deleteTblGoodsCategoryByCategoryIds" parameterType="String">
        delete from tbl_goods_category where category_id in 
        <foreach item="categoryId" collection="array" open="(" separator="," close=")">
            #{categoryId}
        </foreach>
    </delete>
</mapper>