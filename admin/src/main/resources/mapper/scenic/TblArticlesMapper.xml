<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.szz.scenic.mapper.TblArticlesMapper">
    
    <resultMap type="TblArticles" id="TblArticlesResult">
        <result property="id"    column="id"    />
        <result property="title"    column="title"    />
        <result property="coverImage"    column="cover_image"    />
        <result property="content"    column="content"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectTblArticlesVo">
        select id, title, cover_image, content, create_by, create_time, update_by, update_time, remark from tbl_articles
    </sql>

    <select id="selectTblArticlesList" parameterType="TblArticles" resultMap="TblArticlesResult">
        <include refid="selectTblArticlesVo"/>
        <where>  
            <if test="title != null  and title != ''"> and title like concat('%',#{title},'%')</if>
            <if test="coverImage != null  and coverImage != ''"> and cover_image = #{coverImage}</if>
            <if test="content != null  and content != ''"> and content = #{content}</if>
        </where>
    </select>
    
    <select id="selectTblArticlesById" parameterType="Long" resultMap="TblArticlesResult">
        <include refid="selectTblArticlesVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertTblArticles" parameterType="TblArticles" useGeneratedKeys="true" keyProperty="id">
        insert into tbl_articles
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="title != null and title != ''">title,</if>
            <if test="coverImage != null">cover_image,</if>
            <if test="content != null and content != ''">content,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="title != null and title != ''">#{title},</if>
            <if test="coverImage != null">#{coverImage},</if>
            <if test="content != null and content != ''">#{content},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateTblArticles" parameterType="TblArticles">
        update tbl_articles
        <trim prefix="SET" suffixOverrides=",">
            <if test="title != null and title != ''">title = #{title},</if>
            <if test="coverImage != null">cover_image = #{coverImage},</if>
            <if test="content != null and content != ''">content = #{content},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTblArticlesById" parameterType="Long">
        delete from tbl_articles where id = #{id}
    </delete>

    <delete id="deleteTblArticlesByIds" parameterType="String">
        delete from tbl_articles where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>