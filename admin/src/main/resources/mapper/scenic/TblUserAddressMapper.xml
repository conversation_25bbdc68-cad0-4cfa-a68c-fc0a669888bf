<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.szz.scenic.mapper.TblUserAddressMapper">
    
    <resultMap type="TblUserAddress" id="TblUserAddressResult">
        <result property="addressId"    column="address_id"    />
        <result property="userId"    column="user_id"    />
        <result property="consignee"    column="consignee"    />
        <result property="phone"    column="phone"    />
        <result property="province"    column="province"    />
        <result property="city"    column="city"    />
        <result property="district"    column="district"    />
        <result property="street"    column="street"    />
        <result property="isDefault"    column="is_default"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectTblUserAddressVo">
        select address_id, user_id, consignee, phone, province, city, district, street, is_default, create_by, create_time, update_by, update_time, remark from tbl_user_address
    </sql>

    <select id="selectTblUserAddressList" parameterType="TblUserAddress" resultMap="TblUserAddressResult">
        <include refid="selectTblUserAddressVo"/>
        <where>  
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="consignee != null  and consignee != ''"> and consignee = #{consignee}</if>
            <if test="phone != null  and phone != ''"> and phone = #{phone}</if>
            <if test="province != null  and province != ''"> and province = #{province}</if>
            <if test="city != null  and city != ''"> and city = #{city}</if>
            <if test="district != null  and district != ''"> and district = #{district}</if>
            <if test="street != null  and street != ''"> and street = #{street}</if>
            <if test="isDefault != null "> and is_default = #{isDefault}</if>
        </where>
    </select>
    
    <select id="selectTblUserAddressByAddressId" parameterType="Long" resultMap="TblUserAddressResult">
        <include refid="selectTblUserAddressVo"/>
        where address_id = #{addressId}
    </select>
        
    <insert id="insertTblUserAddress" parameterType="TblUserAddress" useGeneratedKeys="true" keyProperty="addressId">
        insert into tbl_user_address
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="consignee != null and consignee != ''">consignee,</if>
            <if test="phone != null and phone != ''">phone,</if>
            <if test="province != null and province != ''">province,</if>
            <if test="city != null and city != ''">city,</if>
            <if test="district != null and district != ''">district,</if>
            <if test="street != null and street != ''">street,</if>
            <if test="isDefault != null">is_default,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="consignee != null and consignee != ''">#{consignee},</if>
            <if test="phone != null and phone != ''">#{phone},</if>
            <if test="province != null and province != ''">#{province},</if>
            <if test="city != null and city != ''">#{city},</if>
            <if test="district != null and district != ''">#{district},</if>
            <if test="street != null and street != ''">#{street},</if>
            <if test="isDefault != null">#{isDefault},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateTblUserAddress" parameterType="TblUserAddress">
        update tbl_user_address
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="consignee != null and consignee != ''">consignee = #{consignee},</if>
            <if test="phone != null and phone != ''">phone = #{phone},</if>
            <if test="province != null and province != ''">province = #{province},</if>
            <if test="city != null and city != ''">city = #{city},</if>
            <if test="district != null and district != ''">district = #{district},</if>
            <if test="street != null and street != ''">street = #{street},</if>
            <if test="isDefault != null">is_default = #{isDefault},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where address_id = #{addressId}
    </update>

    <delete id="deleteTblUserAddressByAddressId" parameterType="Long">
        delete from tbl_user_address where address_id = #{addressId}
    </delete>

    <delete id="deleteTblUserAddressByAddressIds" parameterType="String">
        delete from tbl_user_address where address_id in 
        <foreach item="addressId" collection="array" open="(" separator="," close=")">
            #{addressId}
        </foreach>
    </delete>
</mapper>