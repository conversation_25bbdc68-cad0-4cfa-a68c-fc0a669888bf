<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.szz.scenic.mapper.TblGoodsOrderMapper">
    
    <resultMap type="TblGoodsOrder" id="TblGoodsOrderResult">
        <result property="orderId"    column="order_id"    />
        <result property="orderSn"    column="order_sn"    />
        <result property="userId"    column="user_id"    />
        <result property="addressId"    column="address_id"    />
        <result property="addressSnapshot"    column="address_snapshot"    />
        <result property="goodsId"    column="goods_id"    />
        <result property="goodsSnapshot"    column="goods_snapshot"    />
        <result property="quantity"    column="quantity"    />
        <result property="totalAmount"    column="total_amount"    />
        <result property="payStatus"    column="pay_status"    />
        <result property="orderStatus"    column="order_status"    />
        <result property="payMethod"    column="pay_method"    />
        <result property="transactionId"    column="transaction_id"    />
        <result property="prepayId"    column="prepay_id"    />
        <result property="payTime"    column="pay_time"    />
        <result property="shippingCompany"    column="shipping_company"    />
        <result property="trackingNumber"    column="tracking_number"    />
        <result property="shippingTime"    column="shipping_time"    />
        <result property="receiveTime"    column="receive_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectTblGoodsOrderVo">
        select order_id, order_sn, user_id, address_id, address_snapshot, goods_id, goods_snapshot, quantity, total_amount, pay_status, order_status, pay_method, transaction_id, prepay_id, pay_time, shipping_company, tracking_number, shipping_time, receive_time, create_by, create_time, update_by, update_time from tbl_goods_order
    </sql>

    <select id="selectTblGoodsOrderList" parameterType="TblGoodsOrder" resultMap="TblGoodsOrderResult">
        <include refid="selectTblGoodsOrderVo"/>
        <where>  
            <if test="orderSn != null  and orderSn != ''"> and order_sn = #{orderSn}</if>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="addressId != null "> and address_id = #{addressId}</if>
            <if test="addressSnapshot != null  and addressSnapshot != ''"> and address_snapshot = #{addressSnapshot}</if>
            <if test="goodsId != null "> and goods_id = #{goodsId}</if>
            <if test="goodsSnapshot != null  and goodsSnapshot != ''"> and goods_snapshot = #{goodsSnapshot}</if>
            <if test="quantity != null "> and quantity = #{quantity}</if>
            <if test="totalAmount != null "> and total_amount = #{totalAmount}</if>
            <if test="payStatus != null  and payStatus != ''"> and pay_status = #{payStatus}</if>
            <if test="orderStatus != null  and orderStatus != ''"> and order_status = #{orderStatus}</if>
            <if test="payMethod != null  and payMethod != ''"> and pay_method = #{payMethod}</if>
            <if test="transactionId != null  and transactionId != ''"> and transaction_id = #{transactionId}</if>
            <if test="prepayId != null  and prepayId != ''"> and prepay_id = #{prepayId}</if>
            <if test="payTime != null "> and pay_time = #{payTime}</if>
            <if test="shippingCompany != null  and shippingCompany != ''"> and shipping_company = #{shippingCompany}</if>
            <if test="trackingNumber != null  and trackingNumber != ''"> and tracking_number = #{trackingNumber}</if>
            <if test="shippingTime != null "> and shipping_time = #{shippingTime}</if>
            <if test="receiveTime != null "> and receive_time = #{receiveTime}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectTblGoodsOrderByOrderId" parameterType="Long" resultMap="TblGoodsOrderResult">
        <include refid="selectTblGoodsOrderVo"/>
        where order_id = #{orderId}
    </select>
        
    <insert id="insertTblGoodsOrder" parameterType="TblGoodsOrder" useGeneratedKeys="true" keyProperty="orderId">
        insert into tbl_goods_order
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orderSn != null and orderSn != ''">order_sn,</if>
            <if test="userId != null">user_id,</if>
            <if test="addressId != null">address_id,</if>
            <if test="addressSnapshot != null">address_snapshot,</if>
            <if test="goodsId != null">goods_id,</if>
            <if test="goodsSnapshot != null">goods_snapshot,</if>
            <if test="quantity != null">quantity,</if>
            <if test="totalAmount != null">total_amount,</if>
            <if test="payStatus != null">pay_status,</if>
            <if test="orderStatus != null">order_status,</if>
            <if test="payMethod != null">pay_method,</if>
            <if test="transactionId != null">transaction_id,</if>
            <if test="prepayId != null">prepay_id,</if>
            <if test="payTime != null">pay_time,</if>
            <if test="shippingCompany != null">shipping_company,</if>
            <if test="trackingNumber != null">tracking_number,</if>
            <if test="shippingTime != null">shipping_time,</if>
            <if test="receiveTime != null">receive_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orderSn != null and orderSn != ''">#{orderSn},</if>
            <if test="userId != null">#{userId},</if>
            <if test="addressId != null">#{addressId},</if>
            <if test="addressSnapshot != null">#{addressSnapshot},</if>
            <if test="goodsId != null">#{goodsId},</if>
            <if test="goodsSnapshot != null">#{goodsSnapshot},</if>
            <if test="quantity != null">#{quantity},</if>
            <if test="totalAmount != null">#{totalAmount},</if>
            <if test="payStatus != null">#{payStatus},</if>
            <if test="orderStatus != null">#{orderStatus},</if>
            <if test="payMethod != null">#{payMethod},</if>
            <if test="transactionId != null">#{transactionId},</if>
            <if test="prepayId != null">#{prepayId},</if>
            <if test="payTime != null">#{payTime},</if>
            <if test="shippingCompany != null">#{shippingCompany},</if>
            <if test="trackingNumber != null">#{trackingNumber},</if>
            <if test="shippingTime != null">#{shippingTime},</if>
            <if test="receiveTime != null">#{receiveTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateTblGoodsOrder" parameterType="TblGoodsOrder">
        update tbl_goods_order
        <trim prefix="SET" suffixOverrides=",">
            <if test="orderSn != null and orderSn != ''">order_sn = #{orderSn},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="addressId != null">address_id = #{addressId},</if>
            <if test="addressSnapshot != null">address_snapshot = #{addressSnapshot},</if>
            <if test="goodsId != null">goods_id = #{goodsId},</if>
            <if test="goodsSnapshot != null">goods_snapshot = #{goodsSnapshot},</if>
            <if test="quantity != null">quantity = #{quantity},</if>
            <if test="totalAmount != null">total_amount = #{totalAmount},</if>
            <if test="payStatus != null">pay_status = #{payStatus},</if>
            <if test="orderStatus != null">order_status = #{orderStatus},</if>
            <if test="payMethod != null">pay_method = #{payMethod},</if>
            <if test="transactionId != null">transaction_id = #{transactionId},</if>
            <if test="prepayId != null">prepay_id = #{prepayId},</if>
            <if test="payTime != null">pay_time = #{payTime},</if>
            <if test="shippingCompany != null">shipping_company = #{shippingCompany},</if>
            <if test="trackingNumber != null">tracking_number = #{trackingNumber},</if>
            <if test="shippingTime != null">shipping_time = #{shippingTime},</if>
            <if test="receiveTime != null">receive_time = #{receiveTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where order_id = #{orderId}
    </update>

    <delete id="deleteTblGoodsOrderByOrderId" parameterType="Long">
        delete from tbl_goods_order where order_id = #{orderId}
    </delete>

    <delete id="deleteTblGoodsOrderByOrderIds" parameterType="String">
        delete from tbl_goods_order where order_id in 
        <foreach item="orderId" collection="array" open="(" separator="," close=")">
            #{orderId}
        </foreach>
    </delete>
</mapper>