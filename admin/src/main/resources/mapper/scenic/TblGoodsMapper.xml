<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.szz.scenic.mapper.TblGoodsMapper">
    
    <resultMap type="TblGoods" id="TblGoodsResult">
        <result property="goodsId"    column="goods_id"    />
        <result property="goodsName"    column="goods_name"    />
        <result property="pic"    column="pic"    />
        <result property="categoryId"    column="category_id"    />
        <result property="stock"    column="stock"    />
        <result property="price"    column="price"    />
        <result property="pointsCost"    column="points_cost"    />
        <result property="description"    column="description"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectTblGoodsVo">
        select goods_id, goods_name, pic, category_id, stock, price, points_cost, description, status, create_by, create_time, update_by, update_time from tbl_goods
    </sql>

    <select id="selectTblGoodsList" parameterType="TblGoods" resultMap="TblGoodsResult">
        <include refid="selectTblGoodsVo"/>
        <where>  
            <if test="goodsName != null  and goodsName != ''"> and goods_name like concat('%', #{goodsName}, '%')</if>
            <if test="pic != null  and pic != ''"> and pic = #{pic}</if>
            <if test="categoryId != null "> and category_id = #{categoryId}</if>
            <if test="stock != null "> and stock = #{stock}</if>
            <if test="price != null "> and price = #{price}</if>
            <if test="pointsCost != null "> and points_cost = #{pointsCost}</if>
            <if test="description != null  and description != ''"> and description = #{description}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
    </select>
    
    <select id="selectTblGoodsByGoodsId" parameterType="Long" resultMap="TblGoodsResult">
        <include refid="selectTblGoodsVo"/>
        where goods_id = #{goodsId}
    </select>
        
    <insert id="insertTblGoods" parameterType="TblGoods" useGeneratedKeys="true" keyProperty="goodsId">
        insert into tbl_goods
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="goodsName != null and goodsName != ''">goods_name,</if>
            <if test="pic != null">pic,</if>
            <if test="categoryId != null">category_id,</if>
            <if test="stock != null">stock,</if>
            <if test="price != null">price,</if>
            <if test="pointsCost != null">points_cost,</if>
            <if test="description != null">description,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="goodsName != null and goodsName != ''">#{goodsName},</if>
            <if test="pic != null">#{pic},</if>
            <if test="categoryId != null">#{categoryId},</if>
            <if test="stock != null">#{stock},</if>
            <if test="price != null">#{price},</if>
            <if test="pointsCost != null">#{pointsCost},</if>
            <if test="description != null">#{description},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateTblGoods" parameterType="TblGoods">
        update tbl_goods
        <trim prefix="SET" suffixOverrides=",">
            <if test="goodsName != null and goodsName != ''">goods_name = #{goodsName},</if>
            <if test="pic != null">pic = #{pic},</if>
            <if test="categoryId != null">category_id = #{categoryId},</if>
            <if test="stock != null">stock = #{stock},</if>
            <if test="price != null">price = #{price},</if>
            <if test="pointsCost != null">points_cost = #{pointsCost},</if>
            <if test="description != null">description = #{description},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where goods_id = #{goodsId}
    </update>

    <delete id="deleteTblGoodsByGoodsId" parameterType="Long">
        delete from tbl_goods where goods_id = #{goodsId}
    </delete>

    <delete id="deleteTblGoodsByGoodsIds" parameterType="String">
        delete from tbl_goods where goods_id in 
        <foreach item="goodsId" collection="array" open="(" separator="," close=")">
            #{goodsId}
        </foreach>
    </delete>
</mapper>