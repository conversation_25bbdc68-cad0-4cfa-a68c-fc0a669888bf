<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.szz.scenic.mapper.TblTicketMapper">
    
    <resultMap type="TblTicket" id="TblTicketResult">
        <result property="ticketId"    column="ticket_id"    />
        <result property="ticketName"    column="ticket_name"    />
        <result property="images" column="images"/>
        <result property="price"    column="price"    />
        <result property="pointsReward"    column="points_reward"    />
        <result property="description"    column="description"    />
        <result property="skuJson"    column="sku_json"    />
        <result property="status"    column="status"    />
        <result property="temId" column="tem_id"/>
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectTblTicketVo">
        select ticket_id, tem_id, images, ticket_name, price, points_reward, description, sku_json, status, create_by, create_time, update_by, update_time from tbl_ticket
    </sql>

    <select id="selectTblTicketList" parameterType="TblTicket" resultMap="TblTicketResult">
        <include refid="selectTblTicketVo"/>
        <where>  
            <if test="ticketName != null  and ticketName != ''"> and ticket_name like concat('%', #{ticketName}, '%')</if>
            <if test="price != null "> and price = #{price}</if>
            <if test="pointsReward != null "> and points_reward = #{pointsReward}</if>
            <if test="description != null  and description != ''"> and description = #{description}</if>
            <if test="skuJson != null  and skuJson != ''"> and sku_json = #{skuJson}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
    </select>
    
    <select id="selectTblTicketByTicketId" parameterType="Long" resultMap="TblTicketResult">
        <include refid="selectTblTicketVo"/>
        where ticket_id = #{ticketId}
    </select>
        
    <insert id="insertTblTicket" parameterType="TblTicket" useGeneratedKeys="true" keyProperty="ticketId">
        insert into tbl_ticket
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="ticketName != null and ticketName != ''">ticket_name,</if>
            <if test="temId != null">tem_id,</if>
            <if test="images != null and images != ''">images,</if>
            <if test="price != null">price,</if>
            <if test="pointsReward != null">points_reward,</if>
            <if test="description != null">description,</if>
            <if test="skuJson != null">sku_json,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="ticketName != null and ticketName != ''">#{ticketName},</if>
            <if test="temId != null">#{temId},</if>
            <if test="images != null and images != ''">#{images},</if>
            <if test="price != null">#{price},</if>
            <if test="pointsReward != null">#{pointsReward},</if>
            <if test="description != null">#{description},</if>
            <if test="skuJson != null">#{skuJson},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateTblTicket" parameterType="TblTicket">
        update tbl_ticket
        <trim prefix="SET" suffixOverrides=",">
            <if test="ticketName != null and ticketName != ''">ticket_name = #{ticketName},</if>
            <if test="temId != null">tem_id = #{temId},</if>
            <if test="images != null and images != ''">images = #{images},</if>
            <if test="price != null">price = #{price},</if>
            <if test="pointsReward != null">points_reward = #{pointsReward},</if>
            <if test="description != null">description = #{description},</if>
            <if test="skuJson != null">sku_json = #{skuJson},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where ticket_id = #{ticketId}
    </update>

    <delete id="deleteTblTicketByTicketId" parameterType="Long">
        delete from tbl_ticket where ticket_id = #{ticketId}
    </delete>

    <delete id="deleteTblTicketByTicketIds" parameterType="String">
        delete from tbl_ticket where ticket_id in 
        <foreach item="ticketId" collection="array" open="(" separator="," close=")">
            #{ticketId}
        </foreach>
    </delete>
</mapper>