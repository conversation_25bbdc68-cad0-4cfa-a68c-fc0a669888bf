# 项目相关配置
ruoyi:
  # 名称
  name: RuoYi
  # 版本
  version: 3.8.7
  # 版权年份
  copyrightYear: 2024
  # 文件路径 示例（ Windows配置D:/ruoyi/uploadPath，Linux配置 /home/<USER>/uploadPath）
  profile: D:/ruoyi/uploadPath
  # 获取ip地址开关
  addressEnabled: false
  # 验证码类型 math 数字计算 char 字符验证
  captchaType: math

  # 文件/对象存储（默认本地；如需云存储，直接在此文件填写秘钥与配置）
  storage: # 存储配置根节点
    provider: local  # 当前存储提供方，可选：local|minio|aliyun|qiniu
    local: # 本地存储配置
      base-path: ${ruoyi.profile} # 本地存储根目录，默认复用 ruoyi.profile
    minio: # MinIO 配置
      endpoint: http://127.0.0.1:9000 # MinIO 服务地址（含协议与端口）
      access-key: minioadmin # MinIO AccessKey（建议改为你的密钥）
      secret-key: minioadmin # MinIO SecretKey（建议改为你的密钥）
      bucket: ruoyi # 目标存储桶名（需已创建或具有自动创建权限）
      region: "" # 区域（可留空）
      secure: false # 是否使用 HTTPS 访问（true/false）
      domain: "" # 自定义访问域名或 CDN（可选）
    aliyun: # 阿里云 OSS 配置
      endpoint: oss-cn-shenzhen.aliyuncs.com # OSS 区域 Endpoint
      access-key-id: LTAI5tMzsBMGMVdFpu3qs4MU # 阿里云 AK（建议替换）
      access-key-secret: ****************************** # 阿里云 SK（建议替换）
      bucket: szzoss # Bucket 名称
      domain: "" # 自定义访问域名或 CDN（可选）
      keep-local-copy: false # 是否保留本地副本（false=仅云端，true=本地+云端双写）
    qiniu: # 七牛云配置
      access-key: yourAk # 七牛 AK（建议替换）
      secret-key: yourSk # 七牛 SK（建议替换）
      bucket: yourBucket # 空间名称
      region: z0 # 区域代码，如 z0(华东)、z1(华北)、z2(华南)、na0(北美)、as0(东南亚)
      domain: "" # 自定义访问域名或 CDN（可选）

  # 短信服务配置（默认关闭）
  sms: # 短信配置根节点
    provider: none # 选择短信服务商：none|tencent|aliyun；设置后会自动启用对应实现
    tencent: # 腾讯云短信配置
      secret-id: yourSecretId # SecretId（建议替换）
      secret-key: yourSecretKey # SecretKey（建议替换）
      sdk-app-id: yourSdkAppId # 短信应用ID（SmsSdkAppId）
      sign-name: 您的签名 # 短信签名内容
      region: ap-guangzhou # 区域，默认 ap-guangzhou
      endpoint: sms.tencentcloudapi.com # API 端点
    aliyun: # 阿里云短信配置
      access-key-id: yourAk # AccessKeyId（建议替换）
      access-key-secret: yourSk # AccessKeySecret（建议替换）
      sign-name: 您的签名 # 短信签名内容
      region-id: cn-hangzhou # 区域ID，默认 cn-hangzhou
      endpoint: dysmsapi.aliyuncs.com # API 端点

  # 邮件发送配置（默认关闭）。开启后需配置 spring.mail.* 基础参数
  mail:
    enabled: false # 是否启用邮件发送
    from-name: "szz" # 发件人显示名（可选）

# 开发环境配置
server:
  # 服务器的HTTP端口，默认为8080
  port: 8080
  servlet:
    # 应用的访问路径
    context-path: /
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
    # 连接数满后的排队数，默认为100
    accept-count: 1000
    threads:
      # tomcat最大线程数，默认为200
      max: 800
      # Tomcat启动初始化的线程数，默认值10
      min-spare: 100

# 日志配置
logging:
  level:
    com.szz: debug
    org.springframework: warn

# 用户配置
user:
  password:
    # 密码最大错误次数
    maxRetryCount: 5
    # 密码锁定时间（默认10分钟）
    lockTime: 10

# Spring配置
spring:
  # 资源信息
  messages:
    # 国际化资源文件路径
    basename: i18n/messages
  profiles:
    active: druid
  # 文件上传
  servlet:
    multipart:
      # 单个文件大小
      max-file-size: 10MB
      # 设置总上传的文件大小
      max-request-size: 20MB
  # 服务模块
  devtools:
    restart:
      # 热部署开关
      enabled: true
  # 邮件配置（SMTP），配合 ruoyi.mail.enabled 使用
  mail:
    host: smtp.qq.com # SMTP 主机，例如：smtp.qq.com、smtp.163.com、smtp.gmail.com
    port: 465 # 465(SSL) 或 587(STARTTLS)
    username: <EMAIL> # 发件邮箱账号
    password: 1231231 # 客户端授权码/专用密码
    protocol: smtp # 协议，通常为 smtp
    default-encoding: UTF-8 # 默认编码
    properties:
      mail:
        debug: false # 打印 SMTP 交互日志便于排查
        smtp:
          auth: true # 启用认证
          localhost: localhost # 显式指定 EHLO/HELO 主机名，避免服务器因主机名无效而拒绝
          ssl:
            enable: true # 465端口建议启用SSL
          starttls:
            enable: false # 587端口建议启用STARTTLS
            required: false
  # Redis 配置
  data:
    redis:
      # 地址
      host: localhost
      # 端口
      port: 6379
      # 数据库索引
      database: 0
      # 密码（无则留空）
      password:
      # 连接超时时间
      timeout: 10s
      lettuce:
        pool:
          # 连接池中的最小空闲连接
          min-idle: 0
          # 连接池中的最大空闲连接
          max-idle: 8
          # 连接池的最大数据库连接数
          max-active: 8
          # 连接池最大阻塞等待时间（负值表示无限制）
          max-wait: -1ms

# token配置
token:
  # 令牌自定义标识
  header: Authorization
  # 令牌密钥
  secret: SzzAdminJwtSecretKey-0123456789ABCDEF-0123456789
  # 令牌有效期（默认30分钟）
  expireTime: 10800

## MyBatis配置
#mybatis:
#  # 搜索指定包别名
#  typeAliasesPackage: com.szz.**.domain
#  # 配置mapper的扫描，找到所有的mapper.xml映射文件
#  mapperLocations: classpath*:mapper/**/*Mapper.xml
#  # 加载全局的配置文件
#  configLocation: classpath:mybatis/mybatis-config.xml
mybatis-plus:
  type-aliases-package: com.szz.**.domain
  mapper-locations: classpath*:mapper/**/*Mapper.xml
  configuration:
    safe-result-handler-enabled: true
    default-executor-type: simple
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl
    call-setters-on-nulls: true
# PageHelper分页插件
pagehelper:
  helperDialect: mysql
  supportMethodsArguments: true
  params: count=countSql

# Swagger配置
swagger:
  # 是否开启swagger
  enabled: true
  # 请求前缀
  pathMapping: /dev-api

# 加解密配置
encrypt:
  # 推荐：SM4 密钥/IV（均为16字节）。
  sm4-key: aZ7mQ9sT3pL6xD1e
  sm4-iv:  H8nK2vB4cR7yM3tP

# Knife4j 配置（可选）
knife4j:
  enable: true
  setting:
    language: zh_cn

# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: /system/notice
  # 匹配链接
  urlPatterns: /system/*,/monitor/*,/tool/*

wechat:
  appId: wx3eebb53f72f5a402
  appSecret: a7575a9bddb91b4e74a5854e04bd0751