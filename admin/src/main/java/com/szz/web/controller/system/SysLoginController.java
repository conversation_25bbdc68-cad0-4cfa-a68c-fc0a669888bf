package com.szz.web.controller.system;

import java.util.List;
import java.util.Map;
import java.util.Set;

import com.szz.common.annotation.Anonymous;
import com.szz.common.annotation.Encrypt;
import com.szz.common.utils.WeChatUtils;
import com.szz.system.service.ISysUserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import com.szz.common.constant.Constants;
import com.szz.common.core.domain.AjaxResult;
import com.szz.common.core.domain.entity.SysMenu;
import com.szz.common.core.domain.entity.SysUser;
import com.szz.common.core.domain.model.LoginBody;
import com.szz.common.utils.SecurityUtils;
import com.szz.framework.web.service.SysLoginService;
import com.szz.framework.web.service.SysPermissionService;
import com.szz.system.service.ISysMenuService;

/**
 * 登录验证
 * 
 * <AUTHOR>
 */
@RestController
@Tag(name = "权限认证接口")
public class SysLoginController
{
    @Autowired
    private SysLoginService loginService;

    @Autowired
    private ISysMenuService menuService;

    @Autowired
    private SysPermissionService permissionService;

    @Autowired
    private ISysUserService userService;

    @Value("${wechat.appId}")
    private String appId;

    @Value("${wechat.appSecret}")
    private String appSecret;

    /**
     * 登录方法
     * 
     * @param loginBody 登录信息
     * @return 结果
     */
    @PostMapping("/login")
    @Encrypt
    @Operation(summary = "用户名密码登录")
    public AjaxResult login(@RequestBody LoginBody loginBody)
    {
        System.out.println(loginBody.getUsername());
        AjaxResult ajax = AjaxResult.success();
        // 生成令牌
        String token = loginService.login(loginBody.getUsername(), loginBody.getPassword(), loginBody.getCode(),
                loginBody.getUuid());
        ajax.put(Constants.TOKEN, token);
        return ajax;
    }

    /**
     * 获取用户信息
     * 
     * @return 用户信息
     */
    @GetMapping("getInfo")
//    @Encrypt
    public AjaxResult getInfo()
    {
        SysUser user = userService.selectUserById(SecurityUtils.getUserId());
        // 角色集合
        Set<String> roles = permissionService.getRolePermission(user);
        // 权限集合
        Set<String> permissions = permissionService.getMenuPermission(user);
        AjaxResult ajax = AjaxResult.success();
        ajax.put("user", user);
        ajax.put("roles", roles);
        ajax.put("permissions", permissions);
        return ajax;
    }

    /**
     * 获取路由信息
     * 
     * @return 路由信息
     */
    @GetMapping("getRouters")
    public AjaxResult getRouters()
    {
        Long userId = SecurityUtils.getUserId();
        List<SysMenu> menus = menuService.selectMenuTreeByUserId(userId);
        return AjaxResult.success(menuService.buildMenus(menus));
    }

    @PostMapping("wxLogin")
    @Anonymous
    @Encrypt
    public AjaxResult wxLogin(@RequestBody Map<String, String> requestBody){
        AjaxResult ajax = AjaxResult.success();
        String code = requestBody.get("code");
        // 新增：用于换取 openId 的 code（参数名兼容 openCode/openidCode）
        String openIdCode = requestBody.get("openCode");
        if (openIdCode == null) {
            openIdCode = requestBody.get("openidCode");
        }
        String phoneNumber = WeChatUtils.getPhoneNumber(appId, appSecret, code);
        String openId = null;
        if (openIdCode != null) {
            openId = WeChatUtils.getOpenid(appId, appSecret, openIdCode);
        }
        String avatar = requestBody.get("avatarUrl");
        String nickName = requestBody.get("nickName");
        String token = loginService.wxLogin(phoneNumber, avatar, nickName, openId);
        ajax.put(Constants.TOKEN, token);
        return ajax;
    }
}
