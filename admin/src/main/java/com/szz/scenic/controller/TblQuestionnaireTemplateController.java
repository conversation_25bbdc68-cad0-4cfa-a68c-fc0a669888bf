package com.szz.scenic.controller;

import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.v3.oas.annotations.*;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.web.bind.annotation.*;
import org.springframework.security.access.prepost.PreAuthorize;

import com.szz.scenic.domain.TblQuestionnaireTemplate;
import com.szz.scenic.service.ITblQuestionnaireTemplateService;
import com.szz.common.core.controller.BaseController;
import com.szz.common.core.domain.AjaxResult;
import com.szz.common.utils.poi.ExcelUtil;
import com.szz.common.enums.BusinessType;
import com.szz.common.annotation.Log;
import com.szz.common.core.page.TableDataInfo;
import com.szz.common.annotation.RateLimiter;
import com.szz.common.enums.LimitType;

/**
 * 问卷模板Controller
 *
 * <AUTHOR>
 * @date 2025-09-10
 */
@RestController
@RequestMapping("/scenic/questionnaireTemplate")
@Tag(name = "问卷模板管理", description = "问卷模板管理")
public class TblQuestionnaireTemplateController extends BaseController {
    @Autowired
    private ITblQuestionnaireTemplateService tblQuestionnaireTemplateService;

/**
 * 查询问卷模板列表
 */
    @GetMapping("/list")
    @Operation(summary = "查询问卷模板列表")
    @RateLimiter(time = 2, count = 1, limitType = LimitType.IP, skipRoles = {"admin"})
    public TableDataInfo list(TblQuestionnaireTemplate tblQuestionnaireTemplate)
    {
        startPage();
        List<TblQuestionnaireTemplate> list = tblQuestionnaireTemplateService.selectTblQuestionnaireTemplateList(tblQuestionnaireTemplate);
        return getDataTable(list);
    }

    /**
     * 导出问卷模板列表
     */
    @Log(title = "问卷模板", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @Operation(summary = "导出问卷模板列表")
    @RateLimiter(time = 5, count = 1, limitType = LimitType.IP, skipRoles = {"admin"})
    public void export(HttpServletResponse response, TblQuestionnaireTemplate tblQuestionnaireTemplate)
    {
        List<TblQuestionnaireTemplate> list = tblQuestionnaireTemplateService.selectTblQuestionnaireTemplateList(tblQuestionnaireTemplate);
        ExcelUtil<TblQuestionnaireTemplate> util = new ExcelUtil<TblQuestionnaireTemplate>(TblQuestionnaireTemplate.class);
        util.exportExcel(response, list, "问卷模板数据");
    }

    /**
     * 获取问卷模板详细信息
     */
    @GetMapping(value = "/{templateId}")
    @Operation(summary = "获取问卷模板详细信息")
    @RateLimiter(time = 2, count = 1, limitType = LimitType.IP, skipRoles = {"admin"})
    public AjaxResult getInfo(@PathVariable(value = "templateId") Long templateId)
    {
        return success(tblQuestionnaireTemplateService.selectTblQuestionnaireTemplateByTemplateId(templateId));
    }

    /**
     * 新增问卷模板
     */
    @Log(title = "问卷模板", businessType = BusinessType.INSERT)
    @PostMapping()
    @Operation(summary = "新增问卷模板")
    @RateLimiter(time = 5, count = 1, limitType = LimitType.IP, skipRoles = {"admin"})
    public AjaxResult add(@RequestBody TblQuestionnaireTemplate tblQuestionnaireTemplate)
    {
        return toAjax(tblQuestionnaireTemplateService.insertTblQuestionnaireTemplate(tblQuestionnaireTemplate));
    }

    /**
     * 修改问卷模板
     */
    @Log(title = "问卷模板", businessType = BusinessType.UPDATE)
    @PutMapping()
    @Operation(summary = "修改问卷模板")
    @RateLimiter(time = 5, count = 1, limitType = LimitType.IP, skipRoles = {"admin"})
    public AjaxResult edit(@RequestBody TblQuestionnaireTemplate tblQuestionnaireTemplate)
    {
        return toAjax(tblQuestionnaireTemplateService.updateTblQuestionnaireTemplate(tblQuestionnaireTemplate));
    }

    /**
     * 删除问卷模板
     */
    @Log(title = "问卷模板", businessType = BusinessType.DELETE)
    @DeleteMapping("/{templateIds}")
    @Operation(summary = "删除问卷模板")
    @RateLimiter(time = 5, count = 1, limitType = LimitType.IP, skipRoles = {"admin"})
    public AjaxResult remove(@PathVariable(value = "templateIds") Long[] templateIds)
    {
        return toAjax(tblQuestionnaireTemplateService.deleteTblQuestionnaireTemplateByTemplateIds(templateIds));
    }
}
