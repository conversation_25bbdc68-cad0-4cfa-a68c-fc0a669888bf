package com.szz.scenic.controller;

import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.v3.oas.annotations.*;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.web.bind.annotation.*;
import org.springframework.security.access.prepost.PreAuthorize;

import com.szz.scenic.domain.TblGoodsCategory;
import com.szz.scenic.service.ITblGoodsCategoryService;
import com.szz.common.core.controller.BaseController;
import com.szz.common.core.domain.AjaxResult;
import com.szz.common.utils.poi.ExcelUtil;
import com.szz.common.enums.BusinessType;
import com.szz.common.annotation.Log;
import com.szz.common.core.page.TableDataInfo;
import com.szz.common.annotation.RateLimiter;
import com.szz.common.enums.LimitType;

/**
 * 商品分类Controller
 *
 * <AUTHOR>
 * @date 2025-09-08
 */
@RestController
@RequestMapping("/scenic/goodsCategory")
@Tag(name = "商品分类管理", description = "商品分类管理")
public class TblGoodsCategoryController extends BaseController {
    @Autowired
    private ITblGoodsCategoryService tblGoodsCategoryService;

/**
 * 查询商品分类列表
 */
    @GetMapping("/list")
    @Operation(summary = "查询商品分类列表")
    public TableDataInfo list(TblGoodsCategory tblGoodsCategory)
    {
        startPage();
        List<TblGoodsCategory> list = tblGoodsCategoryService.selectTblGoodsCategoryList(tblGoodsCategory);
        return getDataTable(list);
    }

    /**
     * 导出商品分类列表
     */
    @Log(title = "商品分类", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @Operation(summary = "导出商品分类列表")
    @RateLimiter(time = 5, count = 1, limitType = LimitType.IP, skipRoles = {"admin"})
    public void export(HttpServletResponse response, TblGoodsCategory tblGoodsCategory)
    {
        List<TblGoodsCategory> list = tblGoodsCategoryService.selectTblGoodsCategoryList(tblGoodsCategory);
        ExcelUtil<TblGoodsCategory> util = new ExcelUtil<TblGoodsCategory>(TblGoodsCategory.class);
        util.exportExcel(response, list, "商品分类数据");
    }

    /**
     * 获取商品分类详细信息
     */
    @GetMapping(value = "/{categoryId}")
    @Operation(summary = "获取商品分类详细信息")
    @RateLimiter(time = 2, count = 1, limitType = LimitType.IP, skipRoles = {"admin"})
    public AjaxResult getInfo(@PathVariable(value = "categoryId") Long categoryId)
    {
        return success(tblGoodsCategoryService.selectTblGoodsCategoryByCategoryId(categoryId));
    }

    /**
     * 新增商品分类
     */
    @Log(title = "商品分类", businessType = BusinessType.INSERT)
    @PostMapping()
    @Operation(summary = "新增商品分类")
    @RateLimiter(time = 5, count = 1, limitType = LimitType.IP, skipRoles = {"admin"})
    public AjaxResult add(@RequestBody TblGoodsCategory tblGoodsCategory)
    {
        return toAjax(tblGoodsCategoryService.insertTblGoodsCategory(tblGoodsCategory));
    }

    /**
     * 修改商品分类
     */
    @Log(title = "商品分类", businessType = BusinessType.UPDATE)
    @PutMapping()
    @Operation(summary = "修改商品分类")
    @RateLimiter(time = 5, count = 1, limitType = LimitType.IP, skipRoles = {"admin"})
    public AjaxResult edit(@RequestBody TblGoodsCategory tblGoodsCategory)
    {
        return toAjax(tblGoodsCategoryService.updateTblGoodsCategory(tblGoodsCategory));
    }

    /**
     * 删除商品分类
     */
    @Log(title = "商品分类", businessType = BusinessType.DELETE)
    @DeleteMapping("/{categoryIds}")
    @Operation(summary = "删除商品分类")
    @RateLimiter(time = 5, count = 1, limitType = LimitType.IP, skipRoles = {"admin"})
    public AjaxResult remove(@PathVariable(value = "categoryIds") Long[] categoryIds)
    {
        return toAjax(tblGoodsCategoryService.deleteTblGoodsCategoryByCategoryIds(categoryIds));
    }
}
