package com.szz.scenic.controller;

import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.v3.oas.annotations.*;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.web.bind.annotation.*;
import org.springframework.security.access.prepost.PreAuthorize;

import com.szz.scenic.domain.TblGoodsOrder;
import com.szz.scenic.service.ITblGoodsOrderService;
import com.szz.common.core.controller.BaseController;
import com.szz.common.core.domain.AjaxResult;
import com.szz.common.utils.poi.ExcelUtil;
import com.szz.common.enums.BusinessType;
import com.szz.common.annotation.Log;
import com.szz.common.core.page.TableDataInfo;
import com.szz.common.annotation.RateLimiter;
import com.szz.common.enums.LimitType;

/**
 * 商品订单Controller
 *
 * <AUTHOR>
 * @date 2025-09-09
 */
@RestController
@RequestMapping("/scenic/goodsOrder")
@Tag(name = "商品订单管理", description = "商品订单管理")
public class TblGoodsOrderController extends BaseController {
    @Autowired
    private ITblGoodsOrderService tblGoodsOrderService;

/**
 * 查询商品订单列表
 */
    @GetMapping("/list")
    @Operation(summary = "查询商品订单列表")
    public TableDataInfo list(TblGoodsOrder tblGoodsOrder)
    {
        startPage();
        List<TblGoodsOrder> list = tblGoodsOrderService.selectTblGoodsOrderList(tblGoodsOrder);
        return getDataTable(list);
    }

    /**
     * 导出商品订单列表
     */
    @Log(title = "商品订单", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @Operation(summary = "导出商品订单列表")
    @RateLimiter(time = 5, count = 1, limitType = LimitType.IP, skipRoles = {"admin"})
    public void export(HttpServletResponse response, TblGoodsOrder tblGoodsOrder)
    {
        List<TblGoodsOrder> list = tblGoodsOrderService.selectTblGoodsOrderList(tblGoodsOrder);
        ExcelUtil<TblGoodsOrder> util = new ExcelUtil<TblGoodsOrder>(TblGoodsOrder.class);
        util.exportExcel(response, list, "商品订单数据");
    }

    /**
     * 获取商品订单详细信息
     */
    @GetMapping(value = "/{orderId}")
    @Operation(summary = "获取商品订单详细信息")
    @RateLimiter(time = 2, count = 1, limitType = LimitType.IP, skipRoles = {"admin"})
    public AjaxResult getInfo(@PathVariable(value = "orderId") Long orderId)
    {
        return success(tblGoodsOrderService.selectTblGoodsOrderByOrderId(orderId));
    }

    /**
     * 新增商品订单
     */
    @Log(title = "商品订单", businessType = BusinessType.INSERT)
    @PostMapping()
    @Operation(summary = "新增商品订单")
    @RateLimiter(time = 5, count = 1, limitType = LimitType.IP, skipRoles = {"admin"})
    public AjaxResult add(@RequestBody TblGoodsOrder tblGoodsOrder)
    {
        return toAjax(tblGoodsOrderService.insertTblGoodsOrder(tblGoodsOrder));
    }

    /**
     * 修改商品订单
     */
    @Log(title = "商品订单", businessType = BusinessType.UPDATE)
    @PutMapping()
    @Operation(summary = "修改商品订单")
    @RateLimiter(time = 5, count = 1, limitType = LimitType.IP, skipRoles = {"admin"})
    public AjaxResult edit(@RequestBody TblGoodsOrder tblGoodsOrder)
    {
        return toAjax(tblGoodsOrderService.updateTblGoodsOrder(tblGoodsOrder));
    }

    /**
     * 删除商品订单
     */
    @Log(title = "商品订单", businessType = BusinessType.DELETE)
    @DeleteMapping("/{orderIds}")
    @Operation(summary = "删除商品订单")
    @RateLimiter(time = 5, count = 1, limitType = LimitType.IP, skipRoles = {"admin"})
    public AjaxResult remove(@PathVariable(value = "orderIds") Long[] orderIds)
    {
        return toAjax(tblGoodsOrderService.deleteTblGoodsOrderByOrderIds(orderIds));
    }
}
