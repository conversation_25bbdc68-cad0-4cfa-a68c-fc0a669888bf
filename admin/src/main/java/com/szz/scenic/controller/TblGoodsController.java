package com.szz.scenic.controller;

import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.v3.oas.annotations.*;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.web.bind.annotation.*;
import org.springframework.security.access.prepost.PreAuthorize;

import com.szz.scenic.domain.TblGoods;
import com.szz.scenic.service.ITblGoodsService;
import com.szz.common.core.controller.BaseController;
import com.szz.common.core.domain.AjaxResult;
import com.szz.common.utils.poi.ExcelUtil;
import com.szz.common.enums.BusinessType;
import com.szz.common.annotation.Log;
import com.szz.common.core.page.TableDataInfo;
import com.szz.common.annotation.RateLimiter;
import com.szz.common.enums.LimitType;

/**
 * 商品信息Controller
 *
 * <AUTHOR>
 * @date 2025-09-08
 */
@RestController
@RequestMapping("/scenic/goods")
@Tag(name = "商品信息管理", description = "商品信息管理")
public class TblGoodsController extends BaseController {
    @Autowired
    private ITblGoodsService tblGoodsService;

/**
 * 查询商品信息列表
 */
    @GetMapping("/list")
    @Operation(summary = "查询商品信息列表")
    public TableDataInfo list(TblGoods tblGoods)
    {
        startPage();
        List<TblGoods> list = tblGoodsService.selectTblGoodsList(tblGoods);
        return getDataTable(list);
    }

    /**
     * 导出商品信息列表
     */
    @Log(title = "商品信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @Operation(summary = "导出商品信息列表")
    @RateLimiter(time = 5, count = 1, limitType = LimitType.IP, skipRoles = {"admin"})
    public void export(HttpServletResponse response, TblGoods tblGoods)
    {
        List<TblGoods> list = tblGoodsService.selectTblGoodsList(tblGoods);
        ExcelUtil<TblGoods> util = new ExcelUtil<TblGoods>(TblGoods.class);
        util.exportExcel(response, list, "商品信息数据");
    }

    /**
     * 获取商品信息详细信息
     */
    @GetMapping(value = "/{goodsId}")
    @Operation(summary = "获取商品信息详细信息")
    @RateLimiter(time = 2, count = 1, limitType = LimitType.IP, skipRoles = {"admin"})
    public AjaxResult getInfo(@PathVariable(value = "goodsId") Long goodsId)
    {
        return success(tblGoodsService.selectTblGoodsByGoodsId(goodsId));
    }

    /**
     * 新增商品信息
     */
    @Log(title = "商品信息", businessType = BusinessType.INSERT)
    @PostMapping()
    @Operation(summary = "新增商品信息")
    @RateLimiter(time = 5, count = 1, limitType = LimitType.IP, skipRoles = {"admin"})
    public AjaxResult add(@RequestBody TblGoods tblGoods)
    {
        return toAjax(tblGoodsService.insertTblGoods(tblGoods));
    }

    /**
     * 修改商品信息
     */
    @Log(title = "商品信息", businessType = BusinessType.UPDATE)
    @PutMapping()
    @Operation(summary = "修改商品信息")
    @RateLimiter(time = 5, count = 1, limitType = LimitType.IP, skipRoles = {"admin"})
    public AjaxResult edit(@RequestBody TblGoods tblGoods)
    {
        return toAjax(tblGoodsService.updateTblGoods(tblGoods));
    }

    /**
     * 删除商品信息
     */
    @Log(title = "商品信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{goodsIds}")
    @Operation(summary = "删除商品信息")
    @RateLimiter(time = 5, count = 1, limitType = LimitType.IP, skipRoles = {"admin"})
    public AjaxResult remove(@PathVariable(value = "goodsIds") Long[] goodsIds)
    {
        return toAjax(tblGoodsService.deleteTblGoodsByGoodsIds(goodsIds));
    }
}
