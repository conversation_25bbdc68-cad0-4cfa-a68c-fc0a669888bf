package com.szz.scenic.controller;

import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.v3.oas.annotations.*;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.web.bind.annotation.*;
import org.springframework.security.access.prepost.PreAuthorize;

import com.szz.scenic.domain.TblArticles;
import com.szz.scenic.service.ITblArticlesService;
import com.szz.common.core.controller.BaseController;
import com.szz.common.core.domain.AjaxResult;
import com.szz.common.utils.poi.ExcelUtil;
import com.szz.common.enums.BusinessType;
import com.szz.common.annotation.Log;
import com.szz.common.core.page.TableDataInfo;
import com.szz.common.annotation.RateLimiter;
import com.szz.common.enums.LimitType;

/**
 * 文章Controller
 *
 * <AUTHOR>
 * @date 2025-09-08
 */
@RestController
@RequestMapping("/scenic/articles")
@Tag(name = "文章管理", description = "文章管理")
public class TblArticlesController extends BaseController {
    @Autowired
    private ITblArticlesService tblArticlesService;

/**
 * 查询文章列表
 */
    @GetMapping("/list")
    @RateLimiter(time = 2, count = 1, limitType = LimitType.IP, skipRoles = {"admin"})
    public TableDataInfo list(TblArticles tblArticles)
    {
        startPage();
        List<TblArticles> list = tblArticlesService.selectTblArticlesList(tblArticles);
        return getDataTable(list);
    }

    /**
     * 导出文章列表
     */
    @Log(title = "文章", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @Operation(summary = "导出文章列表")
    @RateLimiter(time = 5, count = 1, limitType = LimitType.IP, skipRoles = {"admin"})
    public void export(HttpServletResponse response, TblArticles tblArticles)
    {
        List<TblArticles> list = tblArticlesService.selectTblArticlesList(tblArticles);
        ExcelUtil<TblArticles> util = new ExcelUtil<TblArticles>(TblArticles.class);
        util.exportExcel(response, list, "文章数据");
    }

    /**
     * 获取文章详细信息
     */
    @GetMapping(value = "/{id}")
    @Operation(summary = "获取文章详细信息")
    @RateLimiter(time = 2, count = 1, limitType = LimitType.IP, skipRoles = {"admin"})
    public AjaxResult getInfo(@PathVariable(value = "id") Long id)
    {
        return success(tblArticlesService.selectTblArticlesById(id));
    }

    /**
     * 新增文章
     */
    @Log(title = "文章", businessType = BusinessType.INSERT)
    @PostMapping()
    @Operation(summary = "新增文章")
    @RateLimiter(time = 5, count = 1, limitType = LimitType.IP, skipRoles = {"admin"})
    public AjaxResult add(@RequestBody TblArticles tblArticles)
    {
        return toAjax(tblArticlesService.insertTblArticles(tblArticles));
    }

    /**
     * 修改文章
     */
    @Log(title = "文章", businessType = BusinessType.UPDATE)
    @PutMapping()
    @Operation(summary = "修改文章")
    @RateLimiter(time = 5, count = 1, limitType = LimitType.IP, skipRoles = {"admin"})
    public AjaxResult edit(@RequestBody TblArticles tblArticles)
    {
        return toAjax(tblArticlesService.updateTblArticles(tblArticles));
    }

    /**
     * 删除文章
     */
    @Log(title = "文章", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @Operation(summary = "删除文章")
    @RateLimiter(time = 5, count = 1, limitType = LimitType.IP, skipRoles = {"admin"})
    public AjaxResult remove(@PathVariable(value = "ids") Long[] ids)
    {
        return toAjax(tblArticlesService.deleteTblArticlesByIds(ids));
    }
}
