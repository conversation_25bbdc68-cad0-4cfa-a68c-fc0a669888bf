package com.szz.scenic.controller;

import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.v3.oas.annotations.*;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.web.bind.annotation.*;
import org.springframework.security.access.prepost.PreAuthorize;

import com.szz.scenic.domain.TblOrder;
import com.szz.scenic.service.ITblOrderService;
import com.szz.common.core.controller.BaseController;
import com.szz.common.core.domain.AjaxResult;
import com.szz.common.utils.poi.ExcelUtil;
import com.szz.common.enums.BusinessType;
import com.szz.common.annotation.Log;
import com.szz.common.core.page.TableDataInfo;
import com.szz.common.annotation.RateLimiter;
import com.szz.common.enums.LimitType;

/**
 * 订单Controller
 *
 * <AUTHOR>
 * @date 2025-09-07
 */
@RestController
@RequestMapping("/scenic/order")
@Tag(name = "订单管理", description = "订单管理")
public class TblOrderController extends BaseController {
    @Autowired
    private ITblOrderService tblOrderService;

/**
 * 查询订单列表
 */
    @GetMapping("/list")
    @Operation(summary = "查询订单列表")
    public TableDataInfo list(TblOrder tblOrder)
    {
        startPage();
        List<TblOrder> list = tblOrderService.selectTblOrderList(tblOrder);
        return getDataTable(list);
    }

    /**
     * 导出订单列表
     */
    @Log(title = "订单", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @Operation(summary = "导出订单列表")
    @RateLimiter(time = 5, count = 1, limitType = LimitType.IP, skipRoles = {"admin"})
    public void export(HttpServletResponse response, TblOrder tblOrder)
    {
        List<TblOrder> list = tblOrderService.selectTblOrderList(tblOrder);
        ExcelUtil<TblOrder> util = new ExcelUtil<TblOrder>(TblOrder.class);
        util.exportExcel(response, list, "订单数据");
    }

    /**
     * 获取订单详细信息
     */
    @GetMapping(value = "/{orderId}")
    @Operation(summary = "获取订单详细信息")
    @RateLimiter(time = 2, count = 1, limitType = LimitType.IP, skipRoles = {"admin"})
    public AjaxResult getInfo(@PathVariable(value = "orderId") Long orderId)
    {
        return success(tblOrderService.selectTblOrderByOrderId(orderId));
    }

    /**
     * 新增订单
     */
    @Log(title = "订单", businessType = BusinessType.INSERT)
    @PostMapping()
    @Operation(summary = "新增订单")
    @RateLimiter(time = 5, count = 1, limitType = LimitType.IP, skipRoles = {"admin"})
    public AjaxResult add(@RequestBody TblOrder tblOrder)
    {
        return toAjax(tblOrderService.insertTblOrder(tblOrder));
    }

    /**
     * 修改订单
     */
    @Log(title = "订单", businessType = BusinessType.UPDATE)
    @PutMapping()
    @Operation(summary = "修改订单")
    @RateLimiter(time = 5, count = 1, limitType = LimitType.IP, skipRoles = {"admin"})
    public AjaxResult edit(@RequestBody TblOrder tblOrder)
    {
        return toAjax(tblOrderService.updateTblOrder(tblOrder));
    }

    /**
     * 删除订单
     */
    @Log(title = "订单", businessType = BusinessType.DELETE)
    @DeleteMapping("/{orderIds}")
    @Operation(summary = "删除订单")
    @RateLimiter(time = 5, count = 1, limitType = LimitType.IP, skipRoles = {"admin"})
    public AjaxResult remove(@PathVariable(value = "orderIds") Long[] orderIds)
    {
        return toAjax(tblOrderService.deleteTblOrderByOrderIds(orderIds));
    }
}
