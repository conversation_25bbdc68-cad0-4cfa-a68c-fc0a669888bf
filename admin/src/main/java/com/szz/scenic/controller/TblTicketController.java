package com.szz.scenic.controller;

import java.util.List;

import com.szz.common.annotation.Anonymous;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.v3.oas.annotations.*;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.web.bind.annotation.*;
import org.springframework.security.access.prepost.PreAuthorize;

import com.szz.scenic.domain.TblTicket;
import com.szz.scenic.service.ITblTicketService;
import com.szz.common.core.controller.BaseController;
import com.szz.common.core.domain.AjaxResult;
import com.szz.common.utils.poi.ExcelUtil;
import com.szz.common.enums.BusinessType;
import com.szz.common.annotation.Log;
import com.szz.common.core.page.TableDataInfo;
import com.szz.common.annotation.RateLimiter;
import com.szz.common.enums.LimitType;

/**
 * 门票信息Controller
 *
 * <AUTHOR>
 * @date 2025-09-07
 */
@RestController
@RequestMapping("/scenic/ticket")
@Tag(name = "门票信息管理", description = "门票信息管理")
public class TblTicketController extends BaseController {
    @Autowired
    private ITblTicketService tblTicketService;

/**
 * 查询门票信息列表
 */
    @GetMapping("/list")
    @Operation(summary = "查询门票信息列表")
    @Anonymous
    public TableDataInfo list(TblTicket tblTicket)
    {
        startPage();
        List<TblTicket> list = tblTicketService.selectTblTicketList(tblTicket);
        return getDataTable(list);
    }

    /**
     * 导出门票信息列表
     */
    @Log(title = "门票信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @Operation(summary = "导出门票信息列表")
    @RateLimiter(time = 5, count = 1, limitType = LimitType.IP, skipRoles = {"admin"})
    public void export(HttpServletResponse response, TblTicket tblTicket)
    {
        List<TblTicket> list = tblTicketService.selectTblTicketList(tblTicket);
        ExcelUtil<TblTicket> util = new ExcelUtil<TblTicket>(TblTicket.class);
        util.exportExcel(response, list, "门票信息数据");
    }

    /**
     * 获取门票信息详细信息
     */
    @GetMapping(value = "/{ticketId}")
    @Operation(summary = "获取门票信息详细信息")
    @Anonymous
    @RateLimiter(time = 2, count = 1, limitType = LimitType.IP, skipRoles = {"admin"})
    public AjaxResult getInfo(@PathVariable(value = "ticketId") Long ticketId)
    {
        return success(tblTicketService.selectTblTicketByTicketId(ticketId));
    }

    /**
     * 新增门票信息
     */
    @Log(title = "门票信息", businessType = BusinessType.INSERT)
    @PostMapping()
    @Operation(summary = "新增门票信息")
    @RateLimiter(time = 5, count = 1, limitType = LimitType.IP, skipRoles = {"admin"})
    public AjaxResult add(@RequestBody TblTicket tblTicket)
    {
        return toAjax(tblTicketService.insertTblTicket(tblTicket));
    }

    /**
     * 修改门票信息
     */
    @Log(title = "门票信息", businessType = BusinessType.UPDATE)
    @PutMapping()
    @Operation(summary = "修改门票信息")
    @RateLimiter(time = 5, count = 1, limitType = LimitType.IP, skipRoles = {"admin"})
    public AjaxResult edit(@RequestBody TblTicket tblTicket)
    {
        return toAjax(tblTicketService.updateTblTicket(tblTicket));
    }

    /**
     * 删除门票信息
     */
    @Log(title = "门票信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ticketIds}")
    @Operation(summary = "删除门票信息")
    @RateLimiter(time = 5, count = 1, limitType = LimitType.IP, skipRoles = {"admin"})
    public AjaxResult remove(@PathVariable(value = "ticketIds") Long[] ticketIds)
    {
        return toAjax(tblTicketService.deleteTblTicketByTicketIds(ticketIds));
    }

    @GetMapping("/")
}
