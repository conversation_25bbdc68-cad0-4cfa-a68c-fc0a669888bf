package com.szz.scenic.controller;

import java.util.List;

import com.szz.common.annotation.Anonymous;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.v3.oas.annotations.*;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.web.bind.annotation.*;
import org.springframework.security.access.prepost.PreAuthorize;

import com.szz.scenic.domain.TblSwiper;
import com.szz.scenic.service.ITblSwiperService;
import com.szz.common.core.controller.BaseController;
import com.szz.common.core.domain.AjaxResult;
import com.szz.common.utils.poi.ExcelUtil;
import com.szz.common.enums.BusinessType;
import com.szz.common.annotation.Log;
import com.szz.common.core.page.TableDataInfo;
import com.szz.common.annotation.RateLimiter;
import com.szz.common.enums.LimitType;

/**
 * 轮播图Controller
 *
 * <AUTHOR>
 * @date 2025-09-07
 */
@RestController
@RequestMapping("/scenic/swiper")
@Tag(name = "轮播图管理", description = "轮播图管理")
public class TblSwiperController extends BaseController {
    @Autowired
    private ITblSwiperService tblSwiperService;

/**
 * 查询轮播图列表
 */
    @GetMapping("/list")
    @Operation(summary = "查询轮播图列表")
    @Anonymous
    public TableDataInfo list(TblSwiper tblSwiper)
    {
        startPage();
        List<TblSwiper> list = tblSwiperService.selectTblSwiperList(tblSwiper);
        return getDataTable(list);
    }

    /**
     * 导出轮播图列表
     */
    @Log(title = "轮播图", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @Operation(summary = "导出轮播图列表")
    @RateLimiter(time = 5, count = 1, limitType = LimitType.IP, skipRoles = {"admin"})
    public void export(HttpServletResponse response, TblSwiper tblSwiper)
    {
        List<TblSwiper> list = tblSwiperService.selectTblSwiperList(tblSwiper);
        ExcelUtil<TblSwiper> util = new ExcelUtil<TblSwiper>(TblSwiper.class);
        util.exportExcel(response, list, "轮播图数据");
    }

    /**
     * 获取轮播图详细信息
     */
    @GetMapping(value = "/{id}")
    @Operation(summary = "获取轮播图详细信息")
    @RateLimiter(time = 2, count = 1, limitType = LimitType.IP, skipRoles = {"admin"})
    public AjaxResult getInfo(@PathVariable(value = "id") Long id)
    {
        return success(tblSwiperService.selectTblSwiperById(id));
    }

    /**
     * 新增轮播图
     */
    @Log(title = "轮播图", businessType = BusinessType.INSERT)
    @PostMapping()
    @Operation(summary = "新增轮播图")
    @RateLimiter(time = 5, count = 1, limitType = LimitType.IP, skipRoles = {"admin"})
    public AjaxResult add(@RequestBody TblSwiper tblSwiper)
    {
        return toAjax(tblSwiperService.insertTblSwiper(tblSwiper));
    }

    /**
     * 修改轮播图
     */
    @Log(title = "轮播图", businessType = BusinessType.UPDATE)
    @PutMapping()
    @Operation(summary = "修改轮播图")
    @RateLimiter(time = 5, count = 1, limitType = LimitType.IP, skipRoles = {"admin"})
    public AjaxResult edit(@RequestBody TblSwiper tblSwiper)
    {
        return toAjax(tblSwiperService.updateTblSwiper(tblSwiper));
    }

    /**
     * 删除轮播图
     */
    @Log(title = "轮播图", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @Operation(summary = "删除轮播图")
    @RateLimiter(time = 5, count = 1, limitType = LimitType.IP, skipRoles = {"admin"})
    public AjaxResult remove(@PathVariable(value = "ids") Long[] ids)
    {
        return toAjax(tblSwiperService.deleteTblSwiperByIds(ids));
    }
}
