package com.szz.scenic.controller;

import java.util.List;

import com.szz.scenic.domain.dto.WriteOffDto;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.v3.oas.annotations.*;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.web.bind.annotation.*;
import org.springframework.security.access.prepost.PreAuthorize;

import com.szz.scenic.domain.TblPointsFlow;
import com.szz.scenic.service.ITblPointsFlowService;
import com.szz.common.core.controller.BaseController;
import com.szz.common.core.domain.AjaxResult;
import com.szz.common.utils.poi.ExcelUtil;
import com.szz.common.enums.BusinessType;
import com.szz.common.annotation.Log;
import com.szz.common.core.page.TableDataInfo;
import com.szz.common.annotation.RateLimiter;
import com.szz.common.enums.LimitType;

/**
 * 积分流水Controller
 *
 * <AUTHOR>
 * @date 2025-09-07
 */
@RestController
@RequestMapping("/scenic/flow")
@Tag(name = "积分流水管理", description = "积分流水管理")
public class TblPointsFlowController extends BaseController {
    @Autowired
    private ITblPointsFlowService tblPointsFlowService;

/**
 * 查询积分流水列表
 */
    @GetMapping("/list")
    @Operation(summary = "查询积分流水列表")
    public TableDataInfo list(TblPointsFlow tblPointsFlow)
    {
        startPage();
        List<TblPointsFlow> list = tblPointsFlowService.selectTblPointsFlowList(tblPointsFlow);
        return getDataTable(list);
    }

    /**
     * 导出积分流水列表
     */
    @Log(title = "积分流水", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @Operation(summary = "导出积分流水列表")
    @RateLimiter(time = 5, count = 1, limitType = LimitType.IP, skipRoles = {"admin"})
    public void export(HttpServletResponse response, TblPointsFlow tblPointsFlow)
    {
        List<TblPointsFlow> list = tblPointsFlowService.selectTblPointsFlowList(tblPointsFlow);
        ExcelUtil<TblPointsFlow> util = new ExcelUtil<TblPointsFlow>(TblPointsFlow.class);
        util.exportExcel(response, list, "积分流水数据");
    }

    /**
     * 获取积分流水详细信息
     */
    @GetMapping(value = "/{flowId}")
    @Operation(summary = "获取积分流水详细信息")
    @RateLimiter(time = 2, count = 1, limitType = LimitType.IP, skipRoles = {"admin"})
    public AjaxResult getInfo(@PathVariable(value = "flowId") Long flowId)
    {
        return success(tblPointsFlowService.selectTblPointsFlowByFlowId(flowId));
    }

    /**
     * 新增积分流水
     */
    @Log(title = "积分流水", businessType = BusinessType.INSERT)
    @PostMapping()
    @Operation(summary = "新增积分流水")
    @RateLimiter(time = 5, count = 1, limitType = LimitType.IP, skipRoles = {"admin"})
    public AjaxResult add(@RequestBody TblPointsFlow tblPointsFlow)
    {
        return toAjax(tblPointsFlowService.insertTblPointsFlow(tblPointsFlow));
    }

    /**
     * 修改积分流水
     */
    @Log(title = "积分流水", businessType = BusinessType.UPDATE)
    @PutMapping()
    @Operation(summary = "修改积分流水")
    @RateLimiter(time = 5, count = 1, limitType = LimitType.IP, skipRoles = {"admin"})
    public AjaxResult edit(@RequestBody TblPointsFlow tblPointsFlow)
    {
        return toAjax(tblPointsFlowService.updateTblPointsFlow(tblPointsFlow));
    }

    /**
     * 删除积分流水
     */
    @Log(title = "积分流水", businessType = BusinessType.DELETE)
    @DeleteMapping("/{flowIds}")
    @Operation(summary = "删除积分流水")
    @RateLimiter(time = 5, count = 1, limitType = LimitType.IP, skipRoles = {"admin"})
    public AjaxResult remove(@PathVariable(value = "flowIds") Long[] flowIds)
    {
        return toAjax(tblPointsFlowService.deleteTblPointsFlowByFlowIds(flowIds));
    }

    @PostMapping("/sendPoints")
    public AjaxResult sendPoints(@RequestBody TblPointsFlow tblPointsFlow) {
        return tblPointsFlowService.sendPoints(tblPointsFlow);
    }

    @PostMapping("/writeOff")
    public AjaxResult writeOff(@RequestBody WriteOffDto WriteOffDto) {
        return tblPointsFlowService.writeOff(WriteOffDto);
    }
}
