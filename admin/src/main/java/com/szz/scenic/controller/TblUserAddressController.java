package com.szz.scenic.controller;

import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.v3.oas.annotations.*;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.web.bind.annotation.*;
import org.springframework.security.access.prepost.PreAuthorize;

import com.szz.scenic.domain.TblUserAddress;
import com.szz.scenic.service.ITblUserAddressService;
import com.szz.common.core.controller.BaseController;
import com.szz.common.core.domain.AjaxResult;
import com.szz.common.utils.poi.ExcelUtil;
import com.szz.common.enums.BusinessType;
import com.szz.common.annotation.Log;
import com.szz.common.core.page.TableDataInfo;
import com.szz.common.annotation.RateLimiter;
import com.szz.common.enums.LimitType;

/**
 * 用户地址Controller
 *
 * <AUTHOR>
 * @date 2025-09-09
 */
@RestController
@RequestMapping("/scenic/userAddress")
@Tag(name = "用户地址管理", description = "用户地址管理")
public class TblUserAddressController extends BaseController {
    @Autowired
    private ITblUserAddressService tblUserAddressService;

/**
 * 查询用户地址列表
 */
    @GetMapping("/list")
    @Operation(summary = "查询用户地址列表")
    @RateLimiter(time = 2, count = 1, limitType = LimitType.IP, skipRoles = {"admin"})
    public TableDataInfo list(TblUserAddress tblUserAddress)
    {
        startPage();
        List<TblUserAddress> list = tblUserAddressService.selectTblUserAddressList(tblUserAddress);
        return getDataTable(list);
    }

    /**
     * 导出用户地址列表
     */
    @Log(title = "用户地址", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @Operation(summary = "导出用户地址列表")
    @RateLimiter(time = 5, count = 1, limitType = LimitType.IP, skipRoles = {"admin"})
    public void export(HttpServletResponse response, TblUserAddress tblUserAddress)
    {
        List<TblUserAddress> list = tblUserAddressService.selectTblUserAddressList(tblUserAddress);
        ExcelUtil<TblUserAddress> util = new ExcelUtil<TblUserAddress>(TblUserAddress.class);
        util.exportExcel(response, list, "用户地址数据");
    }

    /**
     * 获取用户地址详细信息
     */
    @GetMapping(value = "/{addressId}")
    @Operation(summary = "获取用户地址详细信息")
    @RateLimiter(time = 2, count = 1, limitType = LimitType.IP, skipRoles = {"admin"})
    public AjaxResult getInfo(@PathVariable(value = "addressId") Long addressId)
    {
        return success(tblUserAddressService.selectTblUserAddressByAddressId(addressId));
    }

    /**
     * 新增用户地址
     */
    @Log(title = "用户地址", businessType = BusinessType.INSERT)
    @PostMapping()
    @Operation(summary = "新增用户地址")
    public AjaxResult add(@RequestBody TblUserAddress tblUserAddress)
    {
        tblUserAddress.setUserId(getUserId());
        return toAjax(tblUserAddressService.insertTblUserAddress(tblUserAddress));
    }

    /**
     * 修改用户地址
     */
    @Log(title = "用户地址", businessType = BusinessType.UPDATE)
    @PutMapping()
    @Operation(summary = "修改用户地址")
    @RateLimiter(time = 5, count = 1, limitType = LimitType.IP, skipRoles = {"admin"})
    public AjaxResult edit(@RequestBody TblUserAddress tblUserAddress)
    {
        return toAjax(tblUserAddressService.updateTblUserAddress(tblUserAddress));
    }

    /**
     * 删除用户地址
     */
    @Log(title = "用户地址", businessType = BusinessType.DELETE)
    @DeleteMapping("/{addressIds}")
    @Operation(summary = "删除用户地址")
    @RateLimiter(time = 5, count = 1, limitType = LimitType.IP, skipRoles = {"admin"})
    public AjaxResult remove(@PathVariable(value = "addressIds") Long[] addressIds)
    {
        return toAjax(tblUserAddressService.deleteTblUserAddressByAddressIds(addressIds));
    }
}
