package com.szz.scenic.domain;

import com.szz.common.annotation.Excel;
import com.baomidou.mybatisplus.annotation.*;
import lombok.*;
import com.szz.common.core.domain.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;



/**
 * 文章对象 tbl_articles
 *
 * <AUTHOR>
 * @date 2025-09-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Schema(name = "TblArticles对象", description = "文章表")
@TableName("tbl_articles")
public class TblArticles extends BaseEntity{
private static final long serialVersionUID=1L;

    /**
     * 文章ID
     */
    @Schema(description = "文章ID")
        @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 文章标题
     */
    @Schema(description = "文章标题")
        @TableField("title")
            @Excel(name = "文章标题")
    private String title;

    /**
     * 封面图片URL
     */
    @Schema(description = "封面图片URL")
        @TableField("cover_image")
            @Excel(name = "封面图片URL")
    private String coverImage;

    /**
     * 文章内容
     */
    @Schema(description = "文章内容")
        @TableField("content")
            @Excel(name = "文章内容")
    private String content;


        }
