package com.szz.scenic.domain;

import com.szz.common.annotation.Excel;
import com.baomidou.mybatisplus.annotation.*;
import lombok.*;
import com.szz.common.core.domain.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;



/**
 * 用户地址对象 tbl_user_address
 *
 * <AUTHOR>
 * @date 2025-09-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Schema(name = "TblUserAddress对象", description = "用户地址表")
@TableName("tbl_user_address")
public class TblUserAddress extends BaseEntity{
private static final long serialVersionUID=1L;

    /**
     * 地址ID
     */
    @Schema(description = "地址ID")
        @TableId(value = "address_id", type = IdType.AUTO)
    private Long addressId;

    /**
     * 所属用户ID
     */
    @Schema(description = "所属用户ID")
        @TableField("user_id")
            @Excel(name = "所属用户ID")
    private Long userId;

    /**
     * 收货人姓名
     */
    @Schema(description = "收货人姓名")
        @TableField("consignee")
            @Excel(name = "收货人姓名")
    private String consignee;

    /**
     * 收货人电话
     */
    @Schema(description = "收货人电话")
        @TableField("phone")
            @Excel(name = "收货人电话")
    private String phone;

    /**
     * 省份
     */
    @Schema(description = "省份")
        @TableField("province")
            @Excel(name = "省份")
    private String province;

    /**
     * 城市
     */
    @Schema(description = "城市")
        @TableField("city")
            @Excel(name = "城市")
    private String city;

    /**
     * 区/县
     */
    @Schema(description = "区/县")
        @TableField("district")
            @Excel(name = "区/县")
    private String district;

    /**
     * 详细街道地址
     */
    @Schema(description = "详细街道地址")
        @TableField("street")
            @Excel(name = "详细街道地址")
    private String street;

    /**
     * 是否为默认地址（1是 0否）
     */
    @Schema(description = "是否为默认地址（1是 0否）")
        @TableField("is_default")
            @Excel(name = "是否为默认地址" , readConverterExp = "1=是,0=否")
    private Integer isDefault;


        }
