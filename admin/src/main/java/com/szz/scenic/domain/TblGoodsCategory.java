package com.szz.scenic.domain;

import com.szz.common.annotation.Excel;
import com.baomidou.mybatisplus.annotation.*;
import lombok.*;
import com.szz.common.core.domain.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;



/**
 * 商品分类对象 tbl_goods_category
 *
 * <AUTHOR>
 * @date 2025-09-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Schema(name = "TblGoodsCategory对象", description = "商品分类表")
@TableName("tbl_goods_category")
public class TblGoodsCategory extends BaseEntity{
private static final long serialVersionUID=1L;

    /**
     * 分类ID
     */
    @Schema(description = "分类ID")
        @TableId(value = "category_id", type = IdType.AUTO)
    private Long categoryId;

    /**
     * 分类名称
     */
    @Schema(description = "分类名称")
        @TableField("category_name")
            @Excel(name = "分类名称")
    private String categoryName;

    /**
     * 分类图标URL
     */
    @Schema(description = "分类图标URL")
        @TableField("icon")
            @Excel(name = "分类图标URL")
    private String icon;


        }
