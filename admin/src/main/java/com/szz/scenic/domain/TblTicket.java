package com.szz.scenic.domain;

    import java.math.BigDecimal;
import com.szz.common.annotation.Excel;
import com.baomidou.mybatisplus.annotation.*;
import lombok.*;
import com.szz.common.core.domain.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;



/**
 * 门票信息对象 tbl_ticket
 *
 * <AUTHOR>
 * @date 2025-09-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Schema(name = "TblTicket对象", description = "门票信息表")
@TableName("tbl_ticket")
public class TblTicket extends BaseEntity{
private static final long serialVersionUID=1L;

    /**
     * 门票ID
     */
    @Schema(description = "门票ID")
        @TableId(value = "ticket_id",type = IdType.AUTO)
    private Long ticketId;

    /**
     * 门票名称
     */
    @Schema(description = "门票名称")
        @TableField("ticket_name")
            @Excel(name = "门票名称")
    private String ticketName;

    /**
     * 图片
     */
    @Schema(description = "图片")
        @TableField("images")
            @Excel(name = "图片")
    private String images;

    /**
     * 门票价格
     */
    @Schema(description = "门票价格")
        @TableField("price")
            @Excel(name = "门票价格")
    private BigDecimal price;

    /**
     * 购票返还积分
     */
    @Schema(description = "购票返还积分")
        @TableField("points_reward")
            @Excel(name = "购票返还积分")
    private BigDecimal pointsReward;

    /**
     * 门票描述
     */
    @Schema(description = "门票描述")
        @TableField("description")
            @Excel(name = "门票描述")
    private String description;

    /**
     * 门票SKU信息，如有效期、价格等
     */
    @Schema(description = "门票SKU信息，如有效期、价格等")
        @TableField("sku_json")
            @Excel(name = "门票SKU信息，如有效期、价格等")
    private String skuJson;

    /**
     * 状态（0上架 1下架）
     */
    @Schema(description = "状态（0上架 1下架）")
        @TableField("status")
            @Excel(name = "状态" , readConverterExp = "0=上架,1=下架")
    private String status;

    /**
     * 问卷调查ID
     */
    @Schema(description = "问卷调查ID")
        @TableField("tem_id")
            @Excel(name = "问卷调查ID")
    private Long temId;


}
