package com.szz.scenic.domain;

import com.szz.common.annotation.Excel;
import com.baomidou.mybatisplus.annotation.*;
import lombok.*;
import com.szz.common.core.domain.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;



/**
 * 轮播图对象 tbl_swiper
 *
 * <AUTHOR>
 * @date 2025-09-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Schema(name = "TblSwiper对象", description = "轮播图表")
@TableName("tbl_swiper")
public class TblSwiper extends BaseEntity{
private static final long serialVersionUID=1L;

    /**
     * 编号
     */
    @Schema(description = "编号")
        @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 图片地址
     */
    @Schema(description = "图片地址")
        @TableField("pic")
            @Excel(name = "图片地址")
    private String pic;

    /**
     * 跳转地址
     */
    @Schema(description = "跳转地址")
        @TableField("nav_to")
            @Excel(name = "跳转地址")
    private String navTo;

    /**
     * 是否跳转外部链接 0否 1是
     */
    @Schema(description = "是否跳转外部链接 0否 1是")
        @TableField("is_external")
            @Excel(name = "是否跳转外部链接 0否 1是")
    private String isExternal;


        }
