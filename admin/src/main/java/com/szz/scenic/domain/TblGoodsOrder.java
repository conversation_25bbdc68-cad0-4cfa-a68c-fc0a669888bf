package com.szz.scenic.domain;

    import java.math.BigDecimal;
    import java.util.Date;
    import com.fasterxml.jackson.annotation.JsonFormat;
import com.szz.common.annotation.Excel;
import com.baomidou.mybatisplus.annotation.*;
import lombok.*;
import com.szz.common.core.domain.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;



/**
 * 商品订单对象 tbl_goods_order
 *
 * <AUTHOR>
 * @date 2025-09-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Schema(name = "TblGoodsOrder对象", description = "商品订单表")
@TableName("tbl_goods_order")
public class TblGoodsOrder extends BaseEntity{
private static final long serialVersionUID=1L;

    /**
     * 订单ID
     */
    @Schema(description = "订单ID")
        @TableId(value = "order_id", type = IdType.AUTO)
    private Long orderId;

    /**
     * 订单编号
     */
    @Schema(description = "订单编号")
        @TableField("order_sn")
            @Excel(name = "订单编号")
    private String orderSn;

    /**
     * 用户ID
     */
    @Schema(description = "用户ID")
        @TableField("user_id")
            @Excel(name = "用户ID")
    private Long userId;

    /**
     * 收货地址ID
     */
    @Schema(description = "收货地址ID")
        @TableField("address_id")
            @Excel(name = "收货地址ID")
    private Long addressId;

    /**
     * 收货地址快照
     */
    @Schema(description = "收货地址快照")
        @TableField("address_snapshot")
            @Excel(name = "收货地址快照")
    private String addressSnapshot;

    /**
     * 商品ID
     */
    @Schema(description = "商品ID")
        @TableField("goods_id")
            @Excel(name = "商品ID")
    private Long goodsId;

    /**
     * 商品快照，包含名称、价格、积分等信息
     */
    @Schema(description = "商品快照，包含名称、价格、积分等信息")
        @TableField("goods_snapshot")
            @Excel(name = "商品快照，包含名称、价格、积分等信息")
    private String goodsSnapshot;

    /**
     * 购买数量
     */
    @Schema(description = "购买数量")
        @TableField("quantity")
            @Excel(name = "购买数量")
    private Long quantity;

    /**
     * 订单总金额
     */
    @Schema(description = "订单总金额")
        @TableField("total_amount")
            @Excel(name = "订单总金额")
    private BigDecimal totalAmount;

    /**
     * 支付状态（0未支付 1已支付）
     */
    @Schema(description = "支付状态（0未支付 1已支付）")
        @TableField("pay_status")
            @Excel(name = "支付状态" , readConverterExp = "0=未支付,1=已支付")
    private String payStatus;

    /**
     * 订单状态（0待付款 1待发货 2已发货 3已完成 4已取消）
     */
    @Schema(description = "订单状态（0待付款 1待发货 2已发货 3已完成 4已取消）")
        @TableField("order_status")
            @Excel(name = "订单状态" , readConverterExp = "0=待付款,1=待发货,2=已发货,3=已完成,4=已取消")
    private String orderStatus;

    /**
     * 支付方式（微信支付或积分支付）
     */
    @Schema(description = "支付方式（微信支付或积分支付）")
        @TableField("pay_method")
            @Excel(name = "支付方式" , readConverterExp = "微=信支付或积分支付")
    private String payMethod;

    /**
     * 支付交易号
     */
    @Schema(description = "支付交易号")
        @TableField("transaction_id")
            @Excel(name = "支付交易号")
    private String transactionId;

    /**
     * 预支付ID
     */
    @Schema(description = "预支付ID")
        @TableField("prepay_id")
            @Excel(name = "预支付ID")
    private String prepayId;

    /**
     * 支付时间
     */
    @Schema(description = "支付时间")
        @TableField("pay_time")
            @JsonFormat(pattern = "yyyy-MM-dd")
            @Excel(name = "支付时间" , width = 30, dateFormat = "yyyy-MM-dd")
    private Date payTime;

    /**
     * 物流公司名称
     */
    @Schema(description = "物流公司名称")
        @TableField("shipping_company")
            @Excel(name = "物流公司名称")
    private String shippingCompany;

    /**
     * 物流单号
     */
    @Schema(description = "物流单号")
        @TableField("tracking_number")
            @Excel(name = "物流单号")
    private String trackingNumber;

    /**
     * 发货时间
     */
    @Schema(description = "发货时间")
        @TableField("shipping_time")
            @JsonFormat(pattern = "yyyy-MM-dd")
            @Excel(name = "发货时间" , width = 30, dateFormat = "yyyy-MM-dd")
    private Date shippingTime;

    /**
     * 确认收货时间
     */
    @Schema(description = "确认收货时间")
        @TableField("receive_time")
            @JsonFormat(pattern = "yyyy-MM-dd")
            @Excel(name = "确认收货时间" , width = 30, dateFormat = "yyyy-MM-dd")
    private Date receiveTime;


        }
