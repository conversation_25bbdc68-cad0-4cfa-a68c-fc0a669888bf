package com.szz.scenic.domain;

    import java.math.BigDecimal;
import com.szz.common.annotation.Excel;
import com.baomidou.mybatisplus.annotation.*;
import lombok.*;
import com.szz.common.core.domain.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;



/**
 * 商品信息对象 tbl_goods
 *
 * <AUTHOR>
 * @date 2025-09-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Schema(name = "TblGoods对象", description = "商品信息表")
@TableName("tbl_goods")
public class TblGoods extends BaseEntity{
private static final long serialVersionUID=1L;

    /**
     * 商品ID
     */
    @Schema(description = "商品ID")
        @TableId(value = "goods_id", type = IdType.AUTO)
    private Long goodsId;

    /**
     * 商品名称
     */
    @Schema(description = "商品名称")
        @TableField("goods_name")
            @Excel(name = "商品名称")
    private String goodsName;

    /**
     * 商品图片
     */
    @Schema(description = "商品图片")
        @TableField("pic")
            @Excel(name = "商品图片")
    private String pic;

    /**
     * 商品分类ID
     */
    @Schema(description = "商品分类ID")
        @TableField("category_id")
            @Excel(name = "商品分类ID")
    private Long categoryId;

    /**
     * 库存
     */
    @Schema(description = "库存")
        @TableField("stock")
            @Excel(name = "库存")
    private Long stock;

    /**
     * 商品价格
     */
    @Schema(description = "商品价格")
        @TableField("price")
            @Excel(name = "商品价格")
    private BigDecimal price;

    /**
     * 兑换所需积分
     */
    @Schema(description = "兑换所需积分")
        @TableField("points_cost")
            @Excel(name = "兑换所需积分")
    private BigDecimal pointsCost;

    /**
     * 商品描述
     */
    @Schema(description = "商品描述")
        @TableField("description")
            @Excel(name = "商品描述")
    private String description;

    /**
     * 商品状态（0上架 1下架）
     */
    @Schema(description = "商品状态（0上架 1下架）")
        @TableField("status")
            @Excel(name = "商品状态" , readConverterExp = "0=上架,1=下架")
    private String status;


        }
