package com.szz.scenic.domain;

import com.szz.common.annotation.Excel;
import com.baomidou.mybatisplus.annotation.*;
import lombok.*;
import com.szz.common.core.domain.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;



/**
 * 问卷模板对象 tbl_questionnaire_template
 *
 * <AUTHOR>
 * @date 2025-09-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Schema(name = "TblQuestionnaireTemplate对象", description = "问卷模板表")
@TableName("tbl_questionnaire_template")
public class TblQuestionnaireTemplate extends BaseEntity{
private static final long serialVersionUID=1L;

    /**
     * 模板ID
     */
    @Schema(description = "模板ID")
        @TableField("template_id")
    private Long templateId;

    /**
     * 问卷模板名称 (如: 门票通用满意度调查)
     */
    @Schema(description = "问卷模板名称 (如: 门票通用满意度调查)")
        @TableField("template_name")
            @Excel(name = "问卷模板名称 (如: 门票通用满意度调查)")
    private String templateName;

    /**
     * 问卷描述
     */
    @Schema(description = "问卷描述")
        @TableField("description")
            @Excel(name = "问卷描述")
    private String description;

    /**
     * 问卷结构定义 (包含所有问题和选项)
     */
    @Schema(description = "问卷结构定义 (包含所有问题和选项)")
        @TableField("structure_json")
            @Excel(name = "问卷结构定义 (包含所有问题和选项)")
    private String structureJson;


        }
