package com.szz.scenic.domain;

    import java.math.BigDecimal;
    import java.util.Date;
    import com.fasterxml.jackson.annotation.JsonFormat;
import com.szz.common.annotation.Excel;
import com.baomidou.mybatisplus.annotation.*;
import lombok.*;
import com.szz.common.core.domain.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;



/**
 * 订单对象 tbl_order
 *
 * <AUTHOR>
 * @date 2025-09-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Schema(name = "TblOrder对象", description = "订单表")
@TableName("tbl_order")
public class TblOrder extends BaseEntity{
private static final long serialVersionUID=1L;

    /**
     * 订单ID
     */
    @Schema(description = "订单ID")
        @TableId(value = "order_id",type = IdType.AUTO)
    private Long orderId;

    /**
     * 订单编号
     */
    @Schema(description = "订单编号")
        @TableField("order_sn")
            @Excel(name = "订单编号")
    private String orderSn;

    /**
     * 用户ID
     */
    @Schema(description = "用户ID")
        @TableField("user_id")
            @Excel(name = "用户ID")
    private Long userId;

    /**
     * 门票ID
     */
    @Schema(description = "门票ID")
        @TableField("ticket_id")
            @Excel(name = "门票ID")
    private Long ticketId;

    /**
     * 所购门票SKU详情，如价格、积分、有效期等
     */
    @Schema(description = "所购门票SKU详情，如价格、积分、有效期等")
        @TableField("ticket_sku_json")
            @Excel(name = "所购门票SKU详情，如价格、积分、有效期等")
    private String ticketSkuJson;

    /**
     * 购买数量
     */
    @Schema(description = "购买数量")
        @TableField("quantity")
            @Excel(name = "购买数量")
    private Long quantity;

    /**
     * 订单总金额
     */
    @Schema(description = "订单总金额")
        @TableField("total_amount")
            @Excel(name = "订单总金额")
    private BigDecimal totalAmount;

    /**
     * 支付状态（0未支付 1已支付）
     */
    @Schema(description = "支付状态（0未支付 1已支付）")
        @TableField("pay_status")
            @Excel(name = "支付状态" , readConverterExp = "0=未支付,1=已支付")
    private String payStatus;

    /**
     * 订单状态（0待付款 1已完成 2已取消）
     */
    @Schema(description = "订单状态（0待付款 1已完成 2已取消）")
        @TableField("order_status")
            @Excel(name = "订单状态" , readConverterExp = "0=待付款,1=已完成,2=已取消")
    private String orderStatus;

    /**
     * 支付方式
     */
    @Schema(description = "支付方式")
        @TableField("pay_method")
            @Excel(name = "支付方式")
    private String payMethod;

    /**
     * 微信支付交易号
     */
    @Schema(description = "微信支付交易号")
        @TableField("transaction_id")
            @Excel(name = "微信支付交易号")
    private String transactionId;

    /**
     * 微信预支付ID
     */
    @Schema(description = "微信预支付ID")
        @TableField("prepay_id")
            @Excel(name = "微信预支付ID")
    private String prepayId;

    /**
     * 支付时间
     */
    @Schema(description = "支付时间")
        @TableField("pay_time")
            @JsonFormat(pattern = "yyyy-MM-dd")
            @Excel(name = "支付时间" , width = 30, dateFormat = "yyyy-MM-dd")
    private Date payTime;

    /**
     * 问卷调查ID
     */
    @Schema(description = "问卷调查ID")
        @TableField("tem_id")
            @Excel(name = "问卷调查ID")
    private Long temId;

    /**
     * 问卷调查结果
     */
    @Schema(description = "问卷调查结果")
        @TableField("questionnaire_result")
            @Excel(name = "问卷调查结果")
    private String questionnaireResult;


}
