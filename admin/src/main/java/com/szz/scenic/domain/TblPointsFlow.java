package com.szz.scenic.domain;

    import java.math.BigDecimal;
    import java.util.List;

    import com.szz.common.annotation.Excel;
import com.baomidou.mybatisplus.annotation.*;
import lombok.*;
import com.szz.common.core.domain.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;



/**
 * 积分流水对象 tbl_points_flow
 *
 * <AUTHOR>
 * @date 2025-09-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Schema(name = "TblPointsFlow对象", description = "积分流水表")
@TableName("tbl_points_flow")
public class TblPointsFlow extends BaseEntity{
private static final long serialVersionUID=1L;

    /**
     * 流水ID
     */
    @Schema(description = "流水ID")
        @TableField("flow_id")
    private Long flowId;

    /**
     * 用户ID
     */
    @Schema(description = "用户ID")
        @TableField("user_id")
            @Excel(name = "用户ID")
    private Long userId;

    /**
     * 流水类型（0收入 1支出）
     */
    @Schema(description = "流水类型（0收入 1支出）")
        @TableField("flow_type")
            @Excel(name = "流水类型" , readConverterExp = "0=收入,1=支出")
    private String flowType;

    /**
     * 来源类型（0购票 1服务消费）
     */
    @Schema(description = "来源类型（0购票 1服务消费）")
        @TableField("source_type")
            @Excel(name = "来源类型" , readConverterExp = "0=购票,1=服务消费")
    private String sourceType;

    /**
     * 来源描述（如：订单编号或服务名称）
     */
    @Schema(description = "来源描述（如：订单编号或服务名称）")
        @TableField("source_desc")
            @Excel(name = "来源描述" , readConverterExp = "如=：订单编号或服务名称")
    private String sourceDesc;

    /**
     * 变动积分数额
     */
    @Schema(description = "变动积分数额")
        @TableField("points_amount")
            @Excel(name = "变动积分数额")
    private BigDecimal pointsAmount;

    /**
     * 变动前积分
     */
    @Schema(description = "变动前积分")
        @TableField("before_points")
            @Excel(name = "变动前积分")
    private BigDecimal beforePoints;

    /**
     * 变动后积分
     */
    @Schema(description = "变动后积分")
        @TableField("after_points")
            @Excel(name = "变动后积分")
    private BigDecimal afterPoints;

    @TableField(exist = false)
    private List<Long> userIds;
    @TableField(exist = false)
    private BigDecimal points;


        }
