package com.szz.scenic.service.impl;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Random;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.szz.common.core.domain.entity.SysUser;
import com.szz.common.utils.DateUtils;
import com.szz.common.utils.SecurityUtils;
import com.szz.common.utils.StringUtils;
import com.szz.scenic.domain.TblPointsFlow;
import com.szz.scenic.domain.TblTicket;
import com.szz.scenic.service.ITblPointsFlowService;
import com.szz.scenic.service.ITblTicketService;
import com.szz.system.mapper.SysUserMapper;
import com.szz.system.service.ISysMenuService;
import com.szz.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.szz.scenic.mapper.TblOrderMapper;
import com.szz.scenic.domain.TblOrder;
import com.szz.scenic.service.ITblOrderService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 订单Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-09-07
 */
@Service
public class TblOrderServiceImpl extends ServiceImpl<TblOrderMapper,TblOrder> implements ITblOrderService {
    @Autowired
    private TblOrderMapper tblOrderMapper;
    @Autowired
    private ITblTicketService ticketService;
    @Autowired
    private ISysUserService userService;
    @Autowired
    private ITblPointsFlowService pointsFlowService;

    /**
     * 查询订单
     *
     * @param orderId 订单主键
     * @return 订单
     */
    @Override
    public TblOrder selectTblOrderByOrderId(Long orderId) {
        return tblOrderMapper.selectTblOrderByOrderId(orderId);
    }

    /**
     * 查询订单列表
     *
     * @param tblOrder 订单
     * @return 订单
     */
    @Override
    public List<TblOrder> selectTblOrderList(TblOrder tblOrder) {
        return tblOrderMapper.selectTblOrderList(tblOrder);
    }

    /**
     * 新增订单
     *
     * @param tblOrder 订单
     * @return 结果
     */
    @Override
    @Transactional // 确保事务一致性
    public int insertTblOrder(TblOrder tblOrder) {

        // 1. 参数校验和数据完整性补充
        if (tblOrder == null || tblOrder.getTicketId() == null) {
            // 如果数据不完整，返回0表示失败
            return 0;
        }

        tblOrder.setUserId(SecurityUtils.getUserId());

        // 2. 查询门票信息并解析SKU，将SKU快照存入订单
        TblTicket ticket = ticketService.getById(tblOrder.getTicketId());
        if (ticket == null || "1".equals(ticket.getStatus())) {
            // 门票不存在或已下架，返回0
            return 0;
        }

        // 假设 tblOrder 中已通过前端传递了选中的 SKU JSON 字符串
        if (StringUtils.isEmpty(tblOrder.getTicketSkuJson())) {
            return 0;
        }

        if (ticket.getTemId()!= null) {
            tblOrder.setTemId(ticket.getTemId());
        }

        // 3. 自动生成订单号、设置状态和支付方式
        // 使用时间戳和随机数生成订单号，保证唯一性
        String orderSn = new SimpleDateFormat("yyyyMMddHHmmssSSS").format(new Date()) + new Random().nextInt(99999);
        tblOrder.setOrderSn(orderSn);

        // 设置订单状态和支付状态为已支付（假设下单即支付）
        tblOrder.setPayStatus("1");
        tblOrder.setOrderStatus("1");
        tblOrder.setPayMethod("微信支付");
        tblOrder.setPayTime(DateUtils.getNowDate());

        // 4. 计算订单总金额和积分（根据SKU快照中的价格）
        BigDecimal pointsEarned;
        try {
            JSONObject selectedSku = JSON.parseObject(tblOrder.getTicketSkuJson());
            BigDecimal price = selectedSku.getBigDecimal("price");
            if (price == null) {
                return 0; // SKU价格不正确
            }

            pointsEarned = selectedSku.getBigDecimal("points_reward");
            if (pointsEarned == null) {
                pointsEarned = BigDecimal.ZERO; // 如果没有定义积分，默认为0
            }

            // 假设 quantity 在 tblOrder 中已传递
            BigDecimal totalAmount = price.multiply(BigDecimal.valueOf(tblOrder.getQuantity()));
            tblOrder.setTotalAmount(totalAmount);
        } catch (Exception e) {
            // 解析JSON失败，返回0
            return 0;
        }

        // 5. 补充通用字段和执行插入
        tblOrder.setCreateTime(DateUtils.getNowDate());
        int rows = tblOrderMapper.insertTblOrder(tblOrder);
        if (rows > 0) {
            // 6. 更新用户积分余额并记录积分流水
            // 若依框架应通过 Service 层来处理业务和缓存
            SysUser user = userService.selectUserById(tblOrder.getUserId());
            if (user != null) {
                BigDecimal beforePoints = user.getPoints();
                BigDecimal afterPoints = beforePoints.add(pointsEarned);

                // 创建积分流水记录
                TblPointsFlow pointsFlow = new TblPointsFlow();
                pointsFlow.setUserId(tblOrder.getUserId());
                pointsFlow.setFlowType("0"); // 0收入
                pointsFlow.setSourceType("0"); // 0购票
                pointsFlow.setSourceDesc("购票订单号：" + tblOrder.getOrderSn());
                pointsFlow.setPointsAmount(pointsEarned);
                pointsFlow.setBeforePoints(beforePoints);
                pointsFlow.setAfterPoints(afterPoints);
                pointsFlow.setCreateTime(DateUtils.getNowDate());
                pointsFlowService.save(pointsFlow);

                // 更新用户积分余额，并确保缓存同步
                user.setPoints(afterPoints);
                userService.updateUser(user);
            }
        }

        return rows;
    }

    /**
     * 修改订单
     *
     * @param tblOrder 订单
     * @return 结果
     */
    @Override
    public int updateTblOrder(TblOrder tblOrder) {
                tblOrder.setUpdateTime(DateUtils.getNowDate());
        return tblOrderMapper.updateTblOrder(tblOrder);
    }

    /**
     * 批量删除订单
     *
     * @param orderIds 需要删除的订单主键
     * @return 结果
     */
    @Override
    public int deleteTblOrderByOrderIds(Long[] orderIds) {
        return tblOrderMapper.deleteTblOrderByOrderIds(orderIds);
    }

    /**
     * 删除订单信息
     *
     * @param orderId 订单主键
     * @return 结果
     */
    @Override
    public int deleteTblOrderByOrderId(Long orderId) {
        return tblOrderMapper.deleteTblOrderByOrderId(orderId);
    }
}
