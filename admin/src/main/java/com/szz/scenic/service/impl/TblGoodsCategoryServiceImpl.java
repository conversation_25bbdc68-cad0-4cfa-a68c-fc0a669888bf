package com.szz.scenic.service.impl;

import java.util.List;
        import com.szz.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.szz.scenic.mapper.TblGoodsCategoryMapper;
import com.szz.scenic.domain.TblGoodsCategory;
import com.szz.scenic.service.ITblGoodsCategoryService;

/**
 * 商品分类Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-09-08
 */
@Service
public class TblGoodsCategoryServiceImpl extends ServiceImpl<TblGoodsCategoryMapper,TblGoodsCategory> implements ITblGoodsCategoryService {
    @Autowired
    private TblGoodsCategoryMapper tblGoodsCategoryMapper;

    /**
     * 查询商品分类
     *
     * @param categoryId 商品分类主键
     * @return 商品分类
     */
    @Override
    public TblGoodsCategory selectTblGoodsCategoryByCategoryId(Long categoryId) {
        return tblGoodsCategoryMapper.selectTblGoodsCategoryByCategoryId(categoryId);
    }

    /**
     * 查询商品分类列表
     *
     * @param tblGoodsCategory 商品分类
     * @return 商品分类
     */
    @Override
    public List<TblGoodsCategory> selectTblGoodsCategoryList(TblGoodsCategory tblGoodsCategory) {
        return tblGoodsCategoryMapper.selectTblGoodsCategoryList(tblGoodsCategory);
    }

    /**
     * 新增商品分类
     *
     * @param tblGoodsCategory 商品分类
     * @return 结果
     */
    @Override
    public int insertTblGoodsCategory(TblGoodsCategory tblGoodsCategory) {
                tblGoodsCategory.setCreateTime(DateUtils.getNowDate());
            return tblGoodsCategoryMapper.insertTblGoodsCategory(tblGoodsCategory);
    }

    /**
     * 修改商品分类
     *
     * @param tblGoodsCategory 商品分类
     * @return 结果
     */
    @Override
    public int updateTblGoodsCategory(TblGoodsCategory tblGoodsCategory) {
                tblGoodsCategory.setUpdateTime(DateUtils.getNowDate());
        return tblGoodsCategoryMapper.updateTblGoodsCategory(tblGoodsCategory);
    }

    /**
     * 批量删除商品分类
     *
     * @param categoryIds 需要删除的商品分类主键
     * @return 结果
     */
    @Override
    public int deleteTblGoodsCategoryByCategoryIds(Long[] categoryIds) {
        return tblGoodsCategoryMapper.deleteTblGoodsCategoryByCategoryIds(categoryIds);
    }

    /**
     * 删除商品分类信息
     *
     * @param categoryId 商品分类主键
     * @return 结果
     */
    @Override
    public int deleteTblGoodsCategoryByCategoryId(Long categoryId) {
        return tblGoodsCategoryMapper.deleteTblGoodsCategoryByCategoryId(categoryId);
    }
}
