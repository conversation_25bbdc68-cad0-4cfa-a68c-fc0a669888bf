package com.szz.scenic.service.impl;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ThreadLocalRandom;

import com.alibaba.fastjson2.JSON;
import com.szz.common.core.domain.entity.SysUser;
import com.szz.common.exception.ServiceException;
import com.szz.common.utils.DateUtils;
import com.szz.common.utils.SecurityUtils;
import com.szz.scenic.domain.TblGoods;
import com.szz.scenic.domain.TblUserAddress;
import com.szz.scenic.service.ITblGoodsService;
import com.szz.scenic.service.ITblUserAddressService;
import com.szz.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.szz.scenic.mapper.TblGoodsOrderMapper;
import com.szz.scenic.domain.TblGoodsOrder;
import com.szz.scenic.service.ITblGoodsOrderService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 商品订单Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-09-09
 */
@Service
public class TblGoodsOrderServiceImpl extends ServiceImpl<TblGoodsOrderMapper,TblGoodsOrder> implements ITblGoodsOrderService {
    @Autowired
    private TblGoodsOrderMapper tblGoodsOrderMapper;
    @Autowired
    private ITblGoodsService goodsService;
    @Autowired
    private ITblUserAddressService addressService;
    @Autowired
    private ISysUserService userService;

    /**
     * 查询商品订单
     *
     * @param orderId 商品订单主键
     * @return 商品订单
     */
    @Override
    public TblGoodsOrder selectTblGoodsOrderByOrderId(Long orderId) {
        return tblGoodsOrderMapper.selectTblGoodsOrderByOrderId(orderId);
    }

    /**
     * 查询商品订单列表
     *
     * @param tblGoodsOrder 商品订单
     * @return 商品订单
     */
    @Override
    public List<TblGoodsOrder> selectTblGoodsOrderList(TblGoodsOrder tblGoodsOrder) {
        return tblGoodsOrderMapper.selectTblGoodsOrderList(tblGoodsOrder);
    }

    /**
     * 创建新的商品订单
     *
     * @param orderRequest 包含前端传来参数的订单请求对象
     * @return 结果，成功时可以返回订单信息或订单ID
     */
    @Override
    @Transactional(rollbackFor = Exception.class) // *** 关键：添加事务管理，确保操作的原子性
    public int insertTblGoodsOrder(TblGoodsOrder orderRequest) {
        // 1. 获取当前用户信息
        Long userId = SecurityUtils.getUserId();
        SysUser currentUser = userService.selectUserById(userId);
        if (currentUser == null) {
            throw new ServiceException("用户未登录或不存在");
        }

        // 2. 校验并获取商品信息
        TblGoods goods = goodsService.getById(orderRequest.getGoodsId());
        if (goods == null) {
            throw new ServiceException("商品不存在或已下架");
        }
        // 检查库存
        if (goods.getStock() < orderRequest.getQuantity()) {
            throw new ServiceException("商品库存不足");
        }

        // 3. 校验并获取收货地址信息
        TblUserAddress address = addressService.getById(orderRequest.getAddressId());
        if (address == null || !address.getUserId().equals(userId)) {
            throw new ServiceException("收货地址无效");
        }

        // 4. 构建订单核心信息
        TblGoodsOrder newOrder = new TblGoodsOrder();
        newOrder.setUserId(userId);
        newOrder.setGoodsId(goods.getGoodsId());
        newOrder.setAddressId(address.getAddressId());
        newOrder.setQuantity(orderRequest.getQuantity());
        newOrder.setPayMethod(orderRequest.getPayMethod());
        newOrder.setCreateTime(DateUtils.getNowDate());

        // 创建数据快照，防止商品或地址信息变更影响历史订单
        newOrder.setGoodsSnapshot(JSON.toJSONString(goods));
        newOrder.setAddressSnapshot(JSON.toJSONString(address));

        // 5. 根据支付方式处理订单金额和用户积分
        if ("积分支付".equals(newOrder.getPayMethod())) {
            // 积分支付逻辑
            BigDecimal requiredPoints = goods.getPrice().multiply(new BigDecimal(newOrder.getQuantity()));
            // 检查用户积分是否足够
            if (requiredPoints.compareTo(currentUser.getPoints()) > 0) {
                throw new ServiceException("用户积分不足");
            }
            // 扣除用户积分 (该方法需要在 ISysUserService 中实现)
            currentUser.setPoints(currentUser.getPoints().subtract(requiredPoints));
            userService.updateUser(currentUser);

            newOrder.setTotalAmount(BigDecimal.ZERO); // 积分支付，订单金额为0
            newOrder.setPayStatus("1"); // 积分支付可视为立即支付成功
            newOrder.setOrderStatus("1"); // 状态变为 "待发货"
            newOrder.setPayTime(new Date());

        } else { // 默认为微信支付等在线支付
            // 在线支付逻辑
            BigDecimal totalAmount = goods.getPrice().multiply(new BigDecimal(newOrder.getQuantity()));
            newOrder.setTotalAmount(totalAmount);
            newOrder.setPayStatus("0"); // 未支付
            newOrder.setOrderStatus("0"); // 待付款
        }

        // 6. 完善订单其他信息
        // 获取当前时间，格式化为 yyyyMMddHHmmssSSS (17位)
        DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS");
        String timePart = timeFormatter.format(LocalDateTime.now());

        // 生成一个6位的随机数， ThreadLocalRandom 在并发环境下性能更好
        int randomPart = ThreadLocalRandom.current().nextInt(100000, 1000000);

        // 拼接成最终的订单号
        String orderSn = timePart + randomPart;
        newOrder.setOrderSn(orderSn);

        // 7. 扣减商品库存
        boolean stockUpdated = goodsService.deductStock(goods.getGoodsId(), newOrder.getQuantity());
        if (!stockUpdated) {
            throw new ServiceException("扣减库存失败，请重试");
        }

        // 8. 插入订单到数据库
        return tblGoodsOrderMapper.insertTblGoodsOrder(newOrder);
    }

    /**
     * 修改商品订单
     *
     * @param tblGoodsOrder 商品订单
     * @return 结果
     */
    @Override
    public int updateTblGoodsOrder(TblGoodsOrder tblGoodsOrder) {
                tblGoodsOrder.setUpdateTime(DateUtils.getNowDate());
        return tblGoodsOrderMapper.updateTblGoodsOrder(tblGoodsOrder);
    }

    /**
     * 批量删除商品订单
     *
     * @param orderIds 需要删除的商品订单主键
     * @return 结果
     */
    @Override
    public int deleteTblGoodsOrderByOrderIds(Long[] orderIds) {
        return tblGoodsOrderMapper.deleteTblGoodsOrderByOrderIds(orderIds);
    }

    /**
     * 删除商品订单信息
     *
     * @param orderId 商品订单主键
     * @return 结果
     */
    @Override
    public int deleteTblGoodsOrderByOrderId(Long orderId) {
        return tblGoodsOrderMapper.deleteTblGoodsOrderByOrderId(orderId);
    }
}
