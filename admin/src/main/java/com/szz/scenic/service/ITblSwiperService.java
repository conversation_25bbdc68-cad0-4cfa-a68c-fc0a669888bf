package com.szz.scenic.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.szz.scenic.domain.TblSwiper;

/**
 * 轮播图Service接口
 *
 * <AUTHOR>
 * @date 2025-09-07
 */
public interface ITblSwiperService extends IService<TblSwiper> {
    /**
     * 查询轮播图
     *
     * @param id 轮播图主键
     * @return 轮播图
     */
    public TblSwiper selectTblSwiperById(Long id);

    /**
     * 查询轮播图列表
     *
     * @param tblSwiper 轮播图
     * @return 轮播图集合
     */
    List<TblSwiper> selectTblSwiperList(TblSwiper tblSwiper);

    /**
     * 新增轮播图
     *
     * @param tblSwiper 轮播图
     * @return 结果
     */
    int insertTblSwiper(TblSwiper tblSwiper);

    /**
     * 修改轮播图
     *
     * @param tblSwiper 轮播图
     * @return 结果
     */
    int updateTblSwiper(TblSwiper tblSwiper);

    /**
     * 批量删除轮播图
     *
     * @param ids 需要删除的轮播图主键集合
     * @return 结果
     */
    int deleteTblSwiperByIds(Long[] ids);

    /**
     * 删除轮播图信息
     *
     * @param id 轮播图主键
     * @return 结果
     */
    int deleteTblSwiperById(Long id);
}
