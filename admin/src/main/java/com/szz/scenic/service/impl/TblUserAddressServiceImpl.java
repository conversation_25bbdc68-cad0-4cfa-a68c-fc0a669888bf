package com.szz.scenic.service.impl;

import java.util.List;
        import com.szz.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.szz.scenic.mapper.TblUserAddressMapper;
import com.szz.scenic.domain.TblUserAddress;
import com.szz.scenic.service.ITblUserAddressService;

/**
 * 用户地址Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-09-09
 */
@Service
public class TblUserAddressServiceImpl extends ServiceImpl<TblUserAddressMapper,TblUserAddress> implements ITblUserAddressService {
    @Autowired
    private TblUserAddressMapper tblUserAddressMapper;

    /**
     * 查询用户地址
     *
     * @param addressId 用户地址主键
     * @return 用户地址
     */
    @Override
    public TblUserAddress selectTblUserAddressByAddressId(Long addressId) {
        return tblUserAddressMapper.selectTblUserAddressByAddressId(addressId);
    }

    /**
     * 查询用户地址列表
     *
     * @param tblUserAddress 用户地址
     * @return 用户地址
     */
    @Override
    public List<TblUserAddress> selectTblUserAddressList(TblUserAddress tblUserAddress) {
        return tblUserAddressMapper.selectTblUserAddressList(tblUserAddress);
    }

    /**
     * 新增用户地址
     *
     * @param tblUserAddress 用户地址
     * @return 结果
     */
    @Override
    public int insertTblUserAddress(TblUserAddress tblUserAddress) {
                tblUserAddress.setCreateTime(DateUtils.getNowDate());
            return tblUserAddressMapper.insertTblUserAddress(tblUserAddress);
    }

    /**
     * 修改用户地址
     *
     * @param tblUserAddress 用户地址
     * @return 结果
     */
    @Override
    public int updateTblUserAddress(TblUserAddress tblUserAddress) {
                tblUserAddress.setUpdateTime(DateUtils.getNowDate());
        return tblUserAddressMapper.updateTblUserAddress(tblUserAddress);
    }

    /**
     * 批量删除用户地址
     *
     * @param addressIds 需要删除的用户地址主键
     * @return 结果
     */
    @Override
    public int deleteTblUserAddressByAddressIds(Long[] addressIds) {
        return tblUserAddressMapper.deleteTblUserAddressByAddressIds(addressIds);
    }

    /**
     * 删除用户地址信息
     *
     * @param addressId 用户地址主键
     * @return 结果
     */
    @Override
    public int deleteTblUserAddressByAddressId(Long addressId) {
        return tblUserAddressMapper.deleteTblUserAddressByAddressId(addressId);
    }
}
