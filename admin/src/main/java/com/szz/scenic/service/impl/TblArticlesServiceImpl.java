package com.szz.scenic.service.impl;

import java.util.List;
        import com.szz.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.szz.scenic.mapper.TblArticlesMapper;
import com.szz.scenic.domain.TblArticles;
import com.szz.scenic.service.ITblArticlesService;

/**
 * 文章Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-09-08
 */
@Service
public class TblArticlesServiceImpl extends ServiceImpl<TblArticlesMapper,TblArticles> implements ITblArticlesService {
    @Autowired
    private TblArticlesMapper tblArticlesMapper;

    /**
     * 查询文章
     *
     * @param id 文章主键
     * @return 文章
     */
    @Override
    public TblArticles selectTblArticlesById(Long id) {
        return tblArticlesMapper.selectTblArticlesById(id);
    }

    /**
     * 查询文章列表
     *
     * @param tblArticles 文章
     * @return 文章
     */
    @Override
    public List<TblArticles> selectTblArticlesList(TblArticles tblArticles) {
        return tblArticlesMapper.selectTblArticlesList(tblArticles);
    }

    /**
     * 新增文章
     *
     * @param tblArticles 文章
     * @return 结果
     */
    @Override
    public int insertTblArticles(TblArticles tblArticles) {
                tblArticles.setCreateTime(DateUtils.getNowDate());
            return tblArticlesMapper.insertTblArticles(tblArticles);
    }

    /**
     * 修改文章
     *
     * @param tblArticles 文章
     * @return 结果
     */
    @Override
    public int updateTblArticles(TblArticles tblArticles) {
                tblArticles.setUpdateTime(DateUtils.getNowDate());
        return tblArticlesMapper.updateTblArticles(tblArticles);
    }

    /**
     * 批量删除文章
     *
     * @param ids 需要删除的文章主键
     * @return 结果
     */
    @Override
    public int deleteTblArticlesByIds(Long[] ids) {
        return tblArticlesMapper.deleteTblArticlesByIds(ids);
    }

    /**
     * 删除文章信息
     *
     * @param id 文章主键
     * @return 结果
     */
    @Override
    public int deleteTblArticlesById(Long id) {
        return tblArticlesMapper.deleteTblArticlesById(id);
    }
}
