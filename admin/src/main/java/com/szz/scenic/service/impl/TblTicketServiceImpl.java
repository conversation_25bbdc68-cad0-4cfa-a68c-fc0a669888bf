package com.szz.scenic.service.impl;

import java.util.List;
        import com.szz.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.szz.scenic.mapper.TblTicketMapper;
import com.szz.scenic.domain.TblTicket;
import com.szz.scenic.service.ITblTicketService;

/**
 * 门票信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-09-07
 */
@Service
public class TblTicketServiceImpl extends ServiceImpl<TblTicketMapper,TblTicket> implements ITblTicketService {
    @Autowired
    private TblTicketMapper tblTicketMapper;

    /**
     * 查询门票信息
     *
     * @param ticketId 门票信息主键
     * @return 门票信息
     */
    @Override
    public TblTicket selectTblTicketByTicketId(Long ticketId) {
        return tblTicketMapper.selectTblTicketByTicketId(ticketId);
    }

    /**
     * 查询门票信息列表
     *
     * @param tblTicket 门票信息
     * @return 门票信息
     */
    @Override
    public List<TblTicket> selectTblTicketList(TblTicket tblTicket) {
        return tblTicketMapper.selectTblTicketList(tblTicket);
    }

    /**
     * 新增门票信息
     *
     * @param tblTicket 门票信息
     * @return 结果
     */
    @Override
    public int insertTblTicket(TblTicket tblTicket) {
                tblTicket.setCreateTime(DateUtils.getNowDate());
            return tblTicketMapper.insertTblTicket(tblTicket);
    }

    /**
     * 修改门票信息
     *
     * @param tblTicket 门票信息
     * @return 结果
     */
    @Override
    public int updateTblTicket(TblTicket tblTicket) {
                tblTicket.setUpdateTime(DateUtils.getNowDate());
        return tblTicketMapper.updateTblTicket(tblTicket);
    }

    /**
     * 批量删除门票信息
     *
     * @param ticketIds 需要删除的门票信息主键
     * @return 结果
     */
    @Override
    public int deleteTblTicketByTicketIds(Long[] ticketIds) {
        return tblTicketMapper.deleteTblTicketByTicketIds(ticketIds);
    }

    /**
     * 删除门票信息信息
     *
     * @param ticketId 门票信息主键
     * @return 结果
     */
    @Override
    public int deleteTblTicketByTicketId(Long ticketId) {
        return tblTicketMapper.deleteTblTicketByTicketId(ticketId);
    }
}
