package com.szz.scenic.service.impl;

import java.util.List;
        import com.szz.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.szz.scenic.mapper.TblQuestionnaireTemplateMapper;
import com.szz.scenic.domain.TblQuestionnaireTemplate;
import com.szz.scenic.service.ITblQuestionnaireTemplateService;

/**
 * 问卷模板Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-09-10
 */
@Service
public class TblQuestionnaireTemplateServiceImpl extends ServiceImpl<TblQuestionnaireTemplateMapper,TblQuestionnaireTemplate> implements ITblQuestionnaireTemplateService {
    @Autowired
    private TblQuestionnaireTemplateMapper tblQuestionnaireTemplateMapper;

    /**
     * 查询问卷模板
     *
     * @param templateId 问卷模板主键
     * @return 问卷模板
     */
    @Override
    public TblQuestionnaireTemplate selectTblQuestionnaireTemplateByTemplateId(Long templateId) {
        return tblQuestionnaireTemplateMapper.selectTblQuestionnaireTemplateByTemplateId(templateId);
    }

    /**
     * 查询问卷模板列表
     *
     * @param tblQuestionnaireTemplate 问卷模板
     * @return 问卷模板
     */
    @Override
    public List<TblQuestionnaireTemplate> selectTblQuestionnaireTemplateList(TblQuestionnaireTemplate tblQuestionnaireTemplate) {
        return tblQuestionnaireTemplateMapper.selectTblQuestionnaireTemplateList(tblQuestionnaireTemplate);
    }

    /**
     * 新增问卷模板
     *
     * @param tblQuestionnaireTemplate 问卷模板
     * @return 结果
     */
    @Override
    public int insertTblQuestionnaireTemplate(TblQuestionnaireTemplate tblQuestionnaireTemplate) {
                tblQuestionnaireTemplate.setCreateTime(DateUtils.getNowDate());
            return tblQuestionnaireTemplateMapper.insertTblQuestionnaireTemplate(tblQuestionnaireTemplate);
    }

    /**
     * 修改问卷模板
     *
     * @param tblQuestionnaireTemplate 问卷模板
     * @return 结果
     */
    @Override
    public int updateTblQuestionnaireTemplate(TblQuestionnaireTemplate tblQuestionnaireTemplate) {
                tblQuestionnaireTemplate.setUpdateTime(DateUtils.getNowDate());
        return tblQuestionnaireTemplateMapper.updateTblQuestionnaireTemplate(tblQuestionnaireTemplate);
    }

    /**
     * 批量删除问卷模板
     *
     * @param templateIds 需要删除的问卷模板主键
     * @return 结果
     */
    @Override
    public int deleteTblQuestionnaireTemplateByTemplateIds(Long[] templateIds) {
        return tblQuestionnaireTemplateMapper.deleteTblQuestionnaireTemplateByTemplateIds(templateIds);
    }

    /**
     * 删除问卷模板信息
     *
     * @param templateId 问卷模板主键
     * @return 结果
     */
    @Override
    public int deleteTblQuestionnaireTemplateByTemplateId(Long templateId) {
        return tblQuestionnaireTemplateMapper.deleteTblQuestionnaireTemplateByTemplateId(templateId);
    }
}
