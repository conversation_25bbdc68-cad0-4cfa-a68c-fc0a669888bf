package com.szz.scenic.service.impl;

import java.util.List;
        import com.szz.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.szz.scenic.mapper.TblGoodsMapper;
import com.szz.scenic.domain.TblGoods;
import com.szz.scenic.service.ITblGoodsService;

/**
 * 商品信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-09-08
 */
@Service
public class TblGoodsServiceImpl extends ServiceImpl<TblGoodsMapper,TblGoods> implements ITblGoodsService {
    @Autowired
    private TblGoodsMapper tblGoodsMapper;

    /**
     * 查询商品信息
     *
     * @param goodsId 商品信息主键
     * @return 商品信息
     */
    @Override
    public TblGoods selectTblGoodsByGoodsId(Long goodsId) {
        return tblGoodsMapper.selectTblGoodsByGoodsId(goodsId);
    }

    /**
     * 查询商品信息列表
     *
     * @param tblGoods 商品信息
     * @return 商品信息
     */
    @Override
    public List<TblGoods> selectTblGoodsList(TblGoods tblGoods) {
        return tblGoodsMapper.selectTblGoodsList(tblGoods);
    }

    /**
     * 新增商品信息
     *
     * @param tblGoods 商品信息
     * @return 结果
     */
    @Override
    public int insertTblGoods(TblGoods tblGoods) {
                tblGoods.setCreateTime(DateUtils.getNowDate());
            return tblGoodsMapper.insertTblGoods(tblGoods);
    }

    /**
     * 修改商品信息
     *
     * @param tblGoods 商品信息
     * @return 结果
     */
    @Override
    public int updateTblGoods(TblGoods tblGoods) {
                tblGoods.setUpdateTime(DateUtils.getNowDate());
        return tblGoodsMapper.updateTblGoods(tblGoods);
    }

    /**
     * 批量删除商品信息
     *
     * @param goodsIds 需要删除的商品信息主键
     * @return 结果
     */
    @Override
    public int deleteTblGoodsByGoodsIds(Long[] goodsIds) {
        return tblGoodsMapper.deleteTblGoodsByGoodsIds(goodsIds);
    }

    /**
     * 删除商品信息信息
     *
     * @param goodsId 商品信息主键
     * @return 结果
     */
    @Override
    public int deleteTblGoodsByGoodsId(Long goodsId) {
        return tblGoodsMapper.deleteTblGoodsByGoodsId(goodsId);
    }

    @Override
    public boolean deductStock(Long goodsId, Long quantity) {
        int i = tblGoodsMapper.deductStock(goodsId, quantity);
        return i > 0;
    }
}
