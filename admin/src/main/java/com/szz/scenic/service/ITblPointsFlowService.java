package com.szz.scenic.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.szz.common.core.domain.AjaxResult;
import com.szz.scenic.domain.TblPointsFlow;
import com.szz.scenic.domain.dto.WriteOffDto;

/**
 * 积分流水Service接口
 *
 * <AUTHOR>
 * @date 2025-09-07
 */
public interface ITblPointsFlowService extends IService<TblPointsFlow> {
    /**
     * 查询积分流水
     *
     * @param flowId 积分流水主键
     * @return 积分流水
     */
    public TblPointsFlow selectTblPointsFlowByFlowId(Long flowId);

    /**
     * 查询积分流水列表
     *
     * @param tblPointsFlow 积分流水
     * @return 积分流水集合
     */
    List<TblPointsFlow> selectTblPointsFlowList(TblPointsFlow tblPointsFlow);

    /**
     * 新增积分流水
     *
     * @param tblPointsFlow 积分流水
     * @return 结果
     */
    int insertTblPointsFlow(TblPointsFlow tblPointsFlow);

    /**
     * 修改积分流水
     *
     * @param tblPointsFlow 积分流水
     * @return 结果
     */
    int updateTblPointsFlow(TblPointsFlow tblPointsFlow);

    /**
     * 批量删除积分流水
     *
     * @param flowIds 需要删除的积分流水主键集合
     * @return 结果
     */
    int deleteTblPointsFlowByFlowIds(Long[] flowIds);

    /**
     * 删除积分流水信息
     *
     * @param flowId 积分流水主键
     * @return 结果
     */
    int deleteTblPointsFlowByFlowId(Long flowId);

    AjaxResult sendPoints(TblPointsFlow tblPointsFlow);

    AjaxResult writeOff(WriteOffDto writeOffDto);
}
