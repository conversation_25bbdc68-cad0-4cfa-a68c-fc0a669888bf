package com.szz.scenic.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.szz.scenic.domain.TblGoods;

/**
 * 商品信息Service接口
 *
 * <AUTHOR>
 * @date 2025-09-08
 */
public interface ITblGoodsService extends IService<TblGoods> {
    /**
     * 查询商品信息
     *
     * @param goodsId 商品信息主键
     * @return 商品信息
     */
    public TblGoods selectTblGoodsByGoodsId(Long goodsId);

    /**
     * 查询商品信息列表
     *
     * @param tblGoods 商品信息
     * @return 商品信息集合
     */
    List<TblGoods> selectTblGoodsList(TblGoods tblGoods);

    /**
     * 新增商品信息
     *
     * @param tblGoods 商品信息
     * @return 结果
     */
    int insertTblGoods(TblGoods tblGoods);

    /**
     * 修改商品信息
     *
     * @param tblGoods 商品信息
     * @return 结果
     */
    int updateTblGoods(TblGoods tblGoods);

    /**
     * 批量删除商品信息
     *
     * @param goodsIds 需要删除的商品信息主键集合
     * @return 结果
     */
    int deleteTblGoodsByGoodsIds(Long[] goodsIds);

    /**
     * 删除商品信息信息
     *
     * @param goodsId 商品信息主键
     * @return 结果
     */
    int deleteTblGoodsByGoodsId(Long goodsId);

    boolean deductStock(Long goodsId, Long quantity);
}
