package com.szz.scenic.service.impl;

import java.util.List;
        import com.szz.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.szz.scenic.mapper.TblSwiperMapper;
import com.szz.scenic.domain.TblSwiper;
import com.szz.scenic.service.ITblSwiperService;

/**
 * 轮播图Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-09-07
 */
@Service
public class TblSwiperServiceImpl extends ServiceImpl<TblSwiperMapper,TblSwiper> implements ITblSwiperService {
    @Autowired
    private TblSwiperMapper tblSwiperMapper;

    /**
     * 查询轮播图
     *
     * @param id 轮播图主键
     * @return 轮播图
     */
    @Override
    public TblSwiper selectTblSwiperById(Long id) {
        return tblSwiperMapper.selectTblSwiperById(id);
    }

    /**
     * 查询轮播图列表
     *
     * @param tblSwiper 轮播图
     * @return 轮播图
     */
    @Override
    public List<TblSwiper> selectTblSwiperList(TblSwiper tblSwiper) {
        return tblSwiperMapper.selectTblSwiperList(tblSwiper);
    }

    /**
     * 新增轮播图
     *
     * @param tblSwiper 轮播图
     * @return 结果
     */
    @Override
    public int insertTblSwiper(TblSwiper tblSwiper) {
                tblSwiper.setCreateTime(DateUtils.getNowDate());
            return tblSwiperMapper.insertTblSwiper(tblSwiper);
    }

    /**
     * 修改轮播图
     *
     * @param tblSwiper 轮播图
     * @return 结果
     */
    @Override
    public int updateTblSwiper(TblSwiper tblSwiper) {
                tblSwiper.setUpdateTime(DateUtils.getNowDate());
        return tblSwiperMapper.updateTblSwiper(tblSwiper);
    }

    /**
     * 批量删除轮播图
     *
     * @param ids 需要删除的轮播图主键
     * @return 结果
     */
    @Override
    public int deleteTblSwiperByIds(Long[] ids) {
        return tblSwiperMapper.deleteTblSwiperByIds(ids);
    }

    /**
     * 删除轮播图信息
     *
     * @param id 轮播图主键
     * @return 结果
     */
    @Override
    public int deleteTblSwiperById(Long id) {
        return tblSwiperMapper.deleteTblSwiperById(id);
    }
}
