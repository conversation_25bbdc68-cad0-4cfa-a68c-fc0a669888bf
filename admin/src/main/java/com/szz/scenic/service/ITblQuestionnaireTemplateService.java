package com.szz.scenic.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.szz.scenic.domain.TblQuestionnaireTemplate;

/**
 * 问卷模板Service接口
 *
 * <AUTHOR>
 * @date 2025-09-10
 */
public interface ITblQuestionnaireTemplateService extends IService<TblQuestionnaireTemplate> {
    /**
     * 查询问卷模板
     *
     * @param templateId 问卷模板主键
     * @return 问卷模板
     */
    public TblQuestionnaireTemplate selectTblQuestionnaireTemplateByTemplateId(Long templateId);

    /**
     * 查询问卷模板列表
     *
     * @param tblQuestionnaireTemplate 问卷模板
     * @return 问卷模板集合
     */
    List<TblQuestionnaireTemplate> selectTblQuestionnaireTemplateList(TblQuestionnaireTemplate tblQuestionnaireTemplate);

    /**
     * 新增问卷模板
     *
     * @param tblQuestionnaireTemplate 问卷模板
     * @return 结果
     */
    int insertTblQuestionnaireTemplate(TblQuestionnaireTemplate tblQuestionnaireTemplate);

    /**
     * 修改问卷模板
     *
     * @param tblQuestionnaireTemplate 问卷模板
     * @return 结果
     */
    int updateTblQuestionnaireTemplate(TblQuestionnaireTemplate tblQuestionnaireTemplate);

    /**
     * 批量删除问卷模板
     *
     * @param templateIds 需要删除的问卷模板主键集合
     * @return 结果
     */
    int deleteTblQuestionnaireTemplateByTemplateIds(Long[] templateIds);

    /**
     * 删除问卷模板信息
     *
     * @param templateId 问卷模板主键
     * @return 结果
     */
    int deleteTblQuestionnaireTemplateByTemplateId(Long templateId);
}
