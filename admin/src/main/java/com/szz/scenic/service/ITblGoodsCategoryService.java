package com.szz.scenic.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.szz.scenic.domain.TblGoodsCategory;

/**
 * 商品分类Service接口
 *
 * <AUTHOR>
 * @date 2025-09-08
 */
public interface ITblGoodsCategoryService extends IService<TblGoodsCategory> {
    /**
     * 查询商品分类
     *
     * @param categoryId 商品分类主键
     * @return 商品分类
     */
    public TblGoodsCategory selectTblGoodsCategoryByCategoryId(Long categoryId);

    /**
     * 查询商品分类列表
     *
     * @param tblGoodsCategory 商品分类
     * @return 商品分类集合
     */
    List<TblGoodsCategory> selectTblGoodsCategoryList(TblGoodsCategory tblGoodsCategory);

    /**
     * 新增商品分类
     *
     * @param tblGoodsCategory 商品分类
     * @return 结果
     */
    int insertTblGoodsCategory(TblGoodsCategory tblGoodsCategory);

    /**
     * 修改商品分类
     *
     * @param tblGoodsCategory 商品分类
     * @return 结果
     */
    int updateTblGoodsCategory(TblGoodsCategory tblGoodsCategory);

    /**
     * 批量删除商品分类
     *
     * @param categoryIds 需要删除的商品分类主键集合
     * @return 结果
     */
    int deleteTblGoodsCategoryByCategoryIds(Long[] categoryIds);

    /**
     * 删除商品分类信息
     *
     * @param categoryId 商品分类主键
     * @return 结果
     */
    int deleteTblGoodsCategoryByCategoryId(Long categoryId);
}
