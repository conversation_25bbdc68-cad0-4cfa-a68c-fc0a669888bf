package com.szz.scenic.service.impl;

import java.util.ArrayList;
import java.util.List;

import com.szz.common.core.domain.AjaxResult;
import com.szz.common.core.domain.entity.SysUser;
import com.szz.common.utils.DateUtils;
import com.szz.scenic.domain.dto.WriteOffDto;
import com.szz.system.mapper.SysUserMapper;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.szz.scenic.mapper.TblPointsFlowMapper;
import com.szz.scenic.domain.TblPointsFlow;
import com.szz.scenic.service.ITblPointsFlowService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 积分流水Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-09-07
 */
@Service
public class TblPointsFlowServiceImpl extends ServiceImpl<TblPointsFlowMapper,TblPointsFlow> implements ITblPointsFlowService {
    @Autowired
    private TblPointsFlowMapper tblPointsFlowMapper;
    @Autowired
    private SysUserMapper userMapper;

    /**
     * 查询积分流水
     *
     * @param flowId 积分流水主键
     * @return 积分流水
     */
    @Override
    public TblPointsFlow selectTblPointsFlowByFlowId(Long flowId) {
        return tblPointsFlowMapper.selectTblPointsFlowByFlowId(flowId);
    }

    /**
     * 查询积分流水列表
     *
     * @param tblPointsFlow 积分流水
     * @return 积分流水
     */
    @Override
    public List<TblPointsFlow> selectTblPointsFlowList(TblPointsFlow tblPointsFlow) {
        return tblPointsFlowMapper.selectTblPointsFlowList(tblPointsFlow);
    }

    /**
     * 新增积分流水
     *
     * @param tblPointsFlow 积分流水
     * @return 结果
     */
    @Override
    public int insertTblPointsFlow(TblPointsFlow tblPointsFlow) {
                tblPointsFlow.setCreateTime(DateUtils.getNowDate());
            return tblPointsFlowMapper.insertTblPointsFlow(tblPointsFlow);
    }

    /**
     * 修改积分流水
     *
     * @param tblPointsFlow 积分流水
     * @return 结果
     */
    @Override
    public int updateTblPointsFlow(TblPointsFlow tblPointsFlow) {
        return tblPointsFlowMapper.updateTblPointsFlow(tblPointsFlow);
    }

    /**
     * 批量删除积分流水
     *
     * @param flowIds 需要删除的积分流水主键
     * @return 结果
     */
    @Override
    public int deleteTblPointsFlowByFlowIds(Long[] flowIds) {
        return tblPointsFlowMapper.deleteTblPointsFlowByFlowIds(flowIds);
    }

    /**
     * 删除积分流水信息
     *
     * @param flowId 积分流水主键
     * @return 结果
     */
    @Override
    public int deleteTblPointsFlowByFlowId(Long flowId) {
        return tblPointsFlowMapper.deleteTblPointsFlowByFlowId(flowId);
    }

    @Override
    @Transactional
    public AjaxResult sendPoints(TblPointsFlow params) {
        List<TblPointsFlow> flowList = new ArrayList<>();
        List<SysUser> userList = userMapper.selectUserByIds(params.getUserIds());
        userList.forEach(user -> {
            TblPointsFlow flow = new TblPointsFlow();
            flow.setUserId(user.getUserId());
            flow.setBeforePoints(user.getPoints());
            flow.setAfterPoints(user.getPoints().add(params.getPoints()));
            flow.setFlowType("0");
            flow.setSourceDesc(params.getSourceDesc());
            flow.setSourceType("2");
            flow.setPointsAmount(params.getPoints());
            flow.setCreateTime(DateUtils.getNowDate());
            flowList.add(flow);
        });
        userMapper.updatePointsByIds(params.getUserIds(),params.getPoints());
        saveBatch(flowList);
        return AjaxResult.success();
    }

    @Override
    @Transactional
    public AjaxResult writeOff(WriteOffDto writeOffDto) {
        SysUser payUser = userMapper.selectUserById(writeOffDto.getPayUserId());
        SysUser writeUser = userMapper.selectUserById(writeOffDto.getWriteUserId());
        if(payUser.getPoints().compareTo(writeOffDto.getPoints()) < 0){
            return AjaxResult.error("支付用户积分不足");
        }
        TblPointsFlow payFlow = new TblPointsFlow();
        payFlow.setUserId(payUser.getUserId());
        payFlow.setBeforePoints(payUser.getPoints());
        payFlow.setAfterPoints(payUser.getPoints().subtract(writeOffDto.getPoints()));
        payFlow.setFlowType("1");
        payFlow.setSourceDesc("消费");
        payFlow.setSourceType("1");
        payFlow.setPointsAmount(writeOffDto.getPoints());
        payFlow.setCreateTime(DateUtils.getNowDate());
        save(payFlow);
        TblPointsFlow writeFlow = new TblPointsFlow();
        writeFlow.setUserId(writeUser.getUserId());
        writeFlow.setBeforePoints(writeUser.getPoints());
        writeFlow.setAfterPoints(writeUser.getPoints().add(writeOffDto.getPoints()));
        writeFlow.setFlowType("0");
        writeFlow.setSourceDesc("核销");
        writeFlow.setSourceType("0");
        writeFlow.setPointsAmount(writeOffDto.getPoints());
        writeFlow.setCreateTime(DateUtils.getNowDate());
        save(writeFlow);
        payUser.setPoints(payUser.getPoints().subtract(writeOffDto.getPoints()));
        writeUser.setPoints(writeUser.getPoints().add(writeOffDto.getPoints()));
        userMapper.updateUser(payUser);
        userMapper.updateUser(writeUser);
        return AjaxResult.success();
    }
}
