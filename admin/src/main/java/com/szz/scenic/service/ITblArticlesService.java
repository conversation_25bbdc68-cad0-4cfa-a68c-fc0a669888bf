package com.szz.scenic.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.szz.scenic.domain.TblArticles;

/**
 * 文章Service接口
 *
 * <AUTHOR>
 * @date 2025-09-08
 */
public interface ITblArticlesService extends IService<TblArticles> {
    /**
     * 查询文章
     *
     * @param id 文章主键
     * @return 文章
     */
    public TblArticles selectTblArticlesById(Long id);

    /**
     * 查询文章列表
     *
     * @param tblArticles 文章
     * @return 文章集合
     */
    List<TblArticles> selectTblArticlesList(TblArticles tblArticles);

    /**
     * 新增文章
     *
     * @param tblArticles 文章
     * @return 结果
     */
    int insertTblArticles(TblArticles tblArticles);

    /**
     * 修改文章
     *
     * @param tblArticles 文章
     * @return 结果
     */
    int updateTblArticles(TblArticles tblArticles);

    /**
     * 批量删除文章
     *
     * @param ids 需要删除的文章主键集合
     * @return 结果
     */
    int deleteTblArticlesByIds(Long[] ids);

    /**
     * 删除文章信息
     *
     * @param id 文章主键
     * @return 结果
     */
    int deleteTblArticlesById(Long id);
}
