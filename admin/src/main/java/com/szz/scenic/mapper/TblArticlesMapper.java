package com.szz.scenic.mapper;

import java.util.List;

import com.szz.scenic.domain.TblArticles;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * 文章Mapper接口
 *
 * <AUTHOR>
 * @date 2025-09-08
 */
public interface TblArticlesMapper extends BaseMapper<TblArticles> {
    /**
     * 查询文章
     *
     * @param id 文章主键
     * @return 文章
     */
    public TblArticles selectTblArticlesById(Long id);

    /**
     * 查询文章列表
     *
     * @param tblArticles 文章
     * @return 文章集合
     */
    List<TblArticles> selectTblArticlesList(TblArticles tblArticles);

    /**
     * 新增文章
     *
     * @param tblArticles 文章
     * @return 结果
     */
    int insertTblArticles(TblArticles tblArticles);

    /**
     * 修改文章
     *
     * @param tblArticles 文章
     * @return 结果
     */
    int updateTblArticles(TblArticles tblArticles);

    /**
     * 删除文章
     *
     * @param id 文章主键
     * @return 结果
     */
    int deleteTblArticlesById(Long id);

    /**
     * 批量删除文章
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteTblArticlesByIds(Long[] ids);
}
