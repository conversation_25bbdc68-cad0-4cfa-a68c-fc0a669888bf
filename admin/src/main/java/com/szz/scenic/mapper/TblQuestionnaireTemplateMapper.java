package com.szz.scenic.mapper;

import java.util.List;

import com.szz.scenic.domain.TblQuestionnaireTemplate;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * 问卷模板Mapper接口
 *
 * <AUTHOR>
 * @date 2025-09-10
 */
public interface TblQuestionnaireTemplateMapper extends BaseMapper<TblQuestionnaireTemplate> {
    /**
     * 查询问卷模板
     *
     * @param templateId 问卷模板主键
     * @return 问卷模板
     */
    public TblQuestionnaireTemplate selectTblQuestionnaireTemplateByTemplateId(Long templateId);

    /**
     * 查询问卷模板列表
     *
     * @param tblQuestionnaireTemplate 问卷模板
     * @return 问卷模板集合
     */
    List<TblQuestionnaireTemplate> selectTblQuestionnaireTemplateList(TblQuestionnaireTemplate tblQuestionnaireTemplate);

    /**
     * 新增问卷模板
     *
     * @param tblQuestionnaireTemplate 问卷模板
     * @return 结果
     */
    int insertTblQuestionnaireTemplate(TblQuestionnaireTemplate tblQuestionnaireTemplate);

    /**
     * 修改问卷模板
     *
     * @param tblQuestionnaireTemplate 问卷模板
     * @return 结果
     */
    int updateTblQuestionnaireTemplate(TblQuestionnaireTemplate tblQuestionnaireTemplate);

    /**
     * 删除问卷模板
     *
     * @param templateId 问卷模板主键
     * @return 结果
     */
    int deleteTblQuestionnaireTemplateByTemplateId(Long templateId);

    /**
     * 批量删除问卷模板
     *
     * @param templateIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteTblQuestionnaireTemplateByTemplateIds(Long[] templateIds);
}
