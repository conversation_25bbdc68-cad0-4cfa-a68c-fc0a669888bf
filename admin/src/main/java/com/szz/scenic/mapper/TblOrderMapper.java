package com.szz.scenic.mapper;

import java.util.List;

import com.szz.scenic.domain.TblOrder;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * 订单Mapper接口
 *
 * <AUTHOR>
 * @date 2025-09-07
 */
public interface TblOrderMapper extends BaseMapper<TblOrder> {
    /**
     * 查询订单
     *
     * @param orderId 订单主键
     * @return 订单
     */
    public TblOrder selectTblOrderByOrderId(Long orderId);

    /**
     * 查询订单列表
     *
     * @param tblOrder 订单
     * @return 订单集合
     */
    List<TblOrder> selectTblOrderList(TblOrder tblOrder);

    /**
     * 新增订单
     *
     * @param tblOrder 订单
     * @return 结果
     */
    int insertTblOrder(TblOrder tblOrder);

    /**
     * 修改订单
     *
     * @param tblOrder 订单
     * @return 结果
     */
    int updateTblOrder(TblOrder tblOrder);

    /**
     * 删除订单
     *
     * @param orderId 订单主键
     * @return 结果
     */
    int deleteTblOrderByOrderId(Long orderId);

    /**
     * 批量删除订单
     *
     * @param orderIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteTblOrderByOrderIds(Long[] orderIds);
}
