package com.szz.scenic.mapper;

import java.util.List;

import com.szz.scenic.domain.TblUserAddress;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * 用户地址Mapper接口
 *
 * <AUTHOR>
 * @date 2025-09-09
 */
public interface TblUserAddressMapper extends BaseMapper<TblUserAddress> {
    /**
     * 查询用户地址
     *
     * @param addressId 用户地址主键
     * @return 用户地址
     */
    public TblUserAddress selectTblUserAddressByAddressId(Long addressId);

    /**
     * 查询用户地址列表
     *
     * @param tblUserAddress 用户地址
     * @return 用户地址集合
     */
    List<TblUserAddress> selectTblUserAddressList(TblUserAddress tblUserAddress);

    /**
     * 新增用户地址
     *
     * @param tblUserAddress 用户地址
     * @return 结果
     */
    int insertTblUserAddress(TblUserAddress tblUserAddress);

    /**
     * 修改用户地址
     *
     * @param tblUserAddress 用户地址
     * @return 结果
     */
    int updateTblUserAddress(TblUserAddress tblUserAddress);

    /**
     * 删除用户地址
     *
     * @param addressId 用户地址主键
     * @return 结果
     */
    int deleteTblUserAddressByAddressId(Long addressId);

    /**
     * 批量删除用户地址
     *
     * @param addressIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteTblUserAddressByAddressIds(Long[] addressIds);
}
