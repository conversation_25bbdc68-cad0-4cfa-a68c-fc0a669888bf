package com.szz.scenic.mapper;

import java.util.List;

import com.szz.scenic.domain.TblPointsFlow;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * 积分流水Mapper接口
 *
 * <AUTHOR>
 * @date 2025-09-07
 */
public interface TblPointsFlowMapper extends BaseMapper<TblPointsFlow> {
    /**
     * 查询积分流水
     *
     * @param flowId 积分流水主键
     * @return 积分流水
     */
    public TblPointsFlow selectTblPointsFlowByFlowId(Long flowId);

    /**
     * 查询积分流水列表
     *
     * @param tblPointsFlow 积分流水
     * @return 积分流水集合
     */
    List<TblPointsFlow> selectTblPointsFlowList(TblPointsFlow tblPointsFlow);

    /**
     * 新增积分流水
     *
     * @param tblPointsFlow 积分流水
     * @return 结果
     */
    int insertTblPointsFlow(TblPointsFlow tblPointsFlow);

    /**
     * 修改积分流水
     *
     * @param tblPointsFlow 积分流水
     * @return 结果
     */
    int updateTblPointsFlow(TblPointsFlow tblPointsFlow);

    /**
     * 删除积分流水
     *
     * @param flowId 积分流水主键
     * @return 结果
     */
    int deleteTblPointsFlowByFlowId(Long flowId);

    /**
     * 批量删除积分流水
     *
     * @param flowIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteTblPointsFlowByFlowIds(Long[] flowIds);
}
