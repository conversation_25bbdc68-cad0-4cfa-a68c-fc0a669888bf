package com.szz.scenic.mapper;

import java.util.List;

import com.szz.scenic.domain.TblGoodsOrder;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * 商品订单Mapper接口
 *
 * <AUTHOR>
 * @date 2025-09-09
 */
public interface TblGoodsOrderMapper extends BaseMapper<TblGoodsOrder> {
    /**
     * 查询商品订单
     *
     * @param orderId 商品订单主键
     * @return 商品订单
     */
    public TblGoodsOrder selectTblGoodsOrderByOrderId(Long orderId);

    /**
     * 查询商品订单列表
     *
     * @param tblGoodsOrder 商品订单
     * @return 商品订单集合
     */
    List<TblGoodsOrder> selectTblGoodsOrderList(TblGoodsOrder tblGoodsOrder);

    /**
     * 新增商品订单
     *
     * @param tblGoodsOrder 商品订单
     * @return 结果
     */
    int insertTblGoodsOrder(TblGoodsOrder tblGoodsOrder);

    /**
     * 修改商品订单
     *
     * @param tblGoodsOrder 商品订单
     * @return 结果
     */
    int updateTblGoodsOrder(TblGoodsOrder tblGoodsOrder);

    /**
     * 删除商品订单
     *
     * @param orderId 商品订单主键
     * @return 结果
     */
    int deleteTblGoodsOrderByOrderId(Long orderId);

    /**
     * 批量删除商品订单
     *
     * @param orderIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteTblGoodsOrderByOrderIds(Long[] orderIds);
}
