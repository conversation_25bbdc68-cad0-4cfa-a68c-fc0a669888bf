package com.szz.scenic.mapper;

import java.util.List;

import com.szz.scenic.domain.TblTicket;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * 门票信息Mapper接口
 *
 * <AUTHOR>
 * @date 2025-09-07
 */
public interface TblTicketMapper extends BaseMapper<TblTicket> {
    /**
     * 查询门票信息
     *
     * @param ticketId 门票信息主键
     * @return 门票信息
     */
    public TblTicket selectTblTicketByTicketId(Long ticketId);

    /**
     * 查询门票信息列表
     *
     * @param tblTicket 门票信息
     * @return 门票信息集合
     */
    List<TblTicket> selectTblTicketList(TblTicket tblTicket);

    /**
     * 新增门票信息
     *
     * @param tblTicket 门票信息
     * @return 结果
     */
    int insertTblTicket(TblTicket tblTicket);

    /**
     * 修改门票信息
     *
     * @param tblTicket 门票信息
     * @return 结果
     */
    int updateTblTicket(TblTicket tblTicket);

    /**
     * 删除门票信息
     *
     * @param ticketId 门票信息主键
     * @return 结果
     */
    int deleteTblTicketByTicketId(Long ticketId);

    /**
     * 批量删除门票信息
     *
     * @param ticketIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteTblTicketByTicketIds(Long[] ticketIds);
}
