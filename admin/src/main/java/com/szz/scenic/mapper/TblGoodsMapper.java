package com.szz.scenic.mapper;

import java.util.List;

import com.szz.scenic.domain.TblGoods;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

/**
 * 商品信息Mapper接口
 *
 * <AUTHOR>
 * @date 2025-09-08
 */
public interface TblGoodsMapper extends BaseMapper<TblGoods> {
    /**
     * 查询商品信息
     *
     * @param goodsId 商品信息主键
     * @return 商品信息
     */
    public TblGoods selectTblGoodsByGoodsId(Long goodsId);

    /**
     * 查询商品信息列表
     *
     * @param tblGoods 商品信息
     * @return 商品信息集合
     */
    List<TblGoods> selectTblGoodsList(TblGoods tblGoods);

    /**
     * 新增商品信息
     *
     * @param tblGoods 商品信息
     * @return 结果
     */
    int insertTblGoods(TblGoods tblGoods);

    /**
     * 修改商品信息
     *
     * @param tblGoods 商品信息
     * @return 结果
     */
    int updateTblGoods(TblGoods tblGoods);

    /**
     * 删除商品信息
     *
     * @param goodsId 商品信息主键
     * @return 结果
     */
    int deleteTblGoodsByGoodsId(Long goodsId);

    /**
     * 批量删除商品信息
     *
     * @param goodsIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteTblGoodsByGoodsIds(Long[] goodsIds);

    /**
     * 原子性扣减商品库存.
     * <p>
     * 通过在WHERE条件中检查库存数量(stock >= quantity)，可以防止超卖问题。
     * 如果库存足够，则更新成功，返回影响的行数为1。
     * 如果库存不足，则WHERE条件不满足，更新失败，返回影响的行数为0。
     *
     * @param goodsId  商品ID, 对应 tbl_goods.goods_id
     * @param quantity 要扣减的数量
     * @return 数据库中受影响的行数 (1代表成功, 0代表失败)
     */
    @Update("UPDATE tbl_goods SET stock = stock - #{quantity} WHERE goods_id = #{goodsId} AND stock >= #{quantity}")
    int deductStock(@Param("goodsId") Long goodsId, @Param("quantity") Long quantity);
}
