package com.szz.scenic.mapper;

import java.util.List;

import com.szz.scenic.domain.TblSwiper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * 轮播图Mapper接口
 *
 * <AUTHOR>
 * @date 2025-09-07
 */
public interface TblSwiperMapper extends BaseMapper<TblSwiper> {
    /**
     * 查询轮播图
     *
     * @param id 轮播图主键
     * @return 轮播图
     */
    public TblSwiper selectTblSwiperById(Long id);

    /**
     * 查询轮播图列表
     *
     * @param tblSwiper 轮播图
     * @return 轮播图集合
     */
    List<TblSwiper> selectTblSwiperList(TblSwiper tblSwiper);

    /**
     * 新增轮播图
     *
     * @param tblSwiper 轮播图
     * @return 结果
     */
    int insertTblSwiper(TblSwiper tblSwiper);

    /**
     * 修改轮播图
     *
     * @param tblSwiper 轮播图
     * @return 结果
     */
    int updateTblSwiper(TblSwiper tblSwiper);

    /**
     * 删除轮播图
     *
     * @param id 轮播图主键
     * @return 结果
     */
    int deleteTblSwiperById(Long id);

    /**
     * 批量删除轮播图
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteTblSwiperByIds(Long[] ids);
}
