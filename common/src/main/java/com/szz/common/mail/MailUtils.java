package com.szz.common.mail;

import com.szz.common.utils.spring.SpringUtils;

/**
 * 邮件工具：静态方式便于在任意位置调用。
 */
public final class MailUtils
{
    private MailUtils() {}

    public static boolean sendText(String to, String subject, String content)
    {
        MailService service = SpringUtils.getBean(MailService.class);
        return service != null && service.sendText(to, subject, content);
    }

    public static boolean sendHtml(String to, String subject, String html)
    {
        MailService service = SpringUtils.getBean(MailService.class);
        return service != null && service.sendHtml(to, subject, html);
    }
}


