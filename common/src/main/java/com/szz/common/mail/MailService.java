package com.szz.common.mail;

import java.util.Map;

/**
 * 邮件发送服务接口。
 */
public interface MailService
{
    /**
     * 发送纯文本邮件
     */
    boolean sendText(String to, String subject, String content);

    /**
     * 发送HTML邮件
     */
    boolean sendHtml(String to, String subject, String html);

    /**
     * 发送基于模板的邮件（占位实现，调用方可自行渲染后走 sendHtml）
     */
    default boolean sendTemplate(String to, String subject, String templateCode, Map<String, Object> variables)
    {
        return sendHtml(to, subject, String.valueOf(variables));
    }
}


