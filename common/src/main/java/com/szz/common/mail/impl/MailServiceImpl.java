package com.szz.common.mail.impl;

import jakarta.mail.internet.InternetAddress;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.mail.javamail.JavaMailSenderImpl;
import org.springframework.util.StringUtils;
import org.springframework.stereotype.Component;
import com.szz.common.mail.MailProperties;
import com.szz.common.mail.MailService;

import jakarta.mail.MessagingException;
import jakarta.mail.internet.MimeMessage;

/**
 * 基于 Spring Boot Mail 的邮件发送实现。
 * 仅在 ruoyi.mail.enabled=true 时启用。
 */
@Component
@ConditionalOnProperty(prefix = "ruoyi.mail", name = "enabled", havingValue = "true")
public class MailServiceImpl implements MailService
{
    private static final Logger log = LoggerFactory.getLogger(MailServiceImpl.class);

    private final JavaMailSender mailSender;
    private final MailProperties mailProperties;

    public MailServiceImpl(JavaMailSender mailSender, MailProperties mailProperties)
    {
        this.mailSender = mailSender;
        this.mailProperties = mailProperties;
    }

    @Override
    public boolean sendText(String to, String subject, String content)
    {
        try
        {
            SimpleMailMessage message = new SimpleMailMessage();
            String fromAddress = resolveFromAddress();
            if (StringUtils.hasText(fromAddress))
            {
                message.setFrom(fromAddress);
            }
            message.setTo(to);
            message.setSubject(subject);
            message.setText(content);
            mailSender.send(message);
            return true;
        }
        catch (Exception e)
        {
            log.error("发送文本邮件失败", e);
            return false;
        }
    }

    @Override
    public boolean sendHtml(String to, String subject, String html)
    {
        try
        {
            MimeMessage mimeMessage = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(mimeMessage, true, "UTF-8");
            String fromAddress = resolveFromAddress();
            if (StringUtils.hasText(mailProperties.getFromName()))
            {
                helper.setFrom(new InternetAddress(fromAddress, mailProperties.getFromName(), "UTF-8"));
            }
            else if (StringUtils.hasText(fromAddress))
            {
                helper.setFrom(fromAddress);
            }
            helper.setTo(to);
            helper.setSubject(subject);
            helper.setText(html, true);
            mailSender.send(mimeMessage);
            return true;
        }
        catch (MessagingException e)
        {
            log.error("构建HTML邮件失败", e);
            return false;
        }
        catch (Exception e)
        {
            log.error("发送HTML邮件失败", e);
            return false;
        }
    }

    private String resolveFromAddress()
    {
        try
        {
            if (mailSender instanceof JavaMailSenderImpl impl)
            {
                return impl.getUsername();
            }
        }
        catch (Exception ignore)
        {
        }
        return null;
    }
}


