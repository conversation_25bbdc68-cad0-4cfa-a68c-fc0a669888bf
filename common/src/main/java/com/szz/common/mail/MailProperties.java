package com.szz.common.mail;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 邮件配置属性：读取 ruoyi.mail 下的配置。
 */
@Component
@ConfigurationProperties(prefix = "ruoyi.mail")
public class MailProperties
{
    /** 是否启用邮件发送 */
    private boolean enabled = false;
    /** 发件人显示名（可选） */
    private String fromName;

    public boolean isEnabled() { return enabled; }
    public void setEnabled(boolean enabled) { this.enabled = enabled; }
    public String getFromName() { return fromName; }
    public void setFromName(String fromName) { this.fromName = fromName; }
}


