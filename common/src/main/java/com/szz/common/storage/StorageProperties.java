package com.szz.common.storage;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Component
@ConfigurationProperties(prefix = "ruoyi.storage")
public class StorageProperties
{
    /** 当前存储提供方：local|minio|aliyun|qiniu */
    private String provider = "local";

    private final Local local = new Local();
    private final Aliyun aliyun = new Aliyun();

    public String getProvider() { return provider; }
    public void setProvider(String provider) { this.provider = provider; }
    public Local getLocal() { return local; }
    public Aliyun getAliyun() { return aliyun; }

    public static class Local
    {
        private String basePath;
        public String getBasePath() { return basePath; }
        public void setBasePath(String basePath) { this.basePath = basePath; }
    }

    public static class Aliyun
    {
        private String endpoint;
        private String accessKeyId;
        private String accessKeySecret;
        private String bucket;
        private String domain;
        /** 是否保留本地副本（true=双写，false=仅OSS） */
        private boolean keepLocalCopy = true;

        public String getEndpoint() { return endpoint; }
        public void setEndpoint(String endpoint) { this.endpoint = endpoint; }
        public String getAccessKeyId() { return accessKeyId; }
        public void setAccessKeyId(String accessKeyId) { this.accessKeyId = accessKeyId; }
        public String getAccessKeySecret() { return accessKeySecret; }
        public void setAccessKeySecret(String accessKeySecret) { this.accessKeySecret = accessKeySecret; }
        public String getBucket() { return bucket; }
        public void setBucket(String bucket) { this.bucket = bucket; }
        public String getDomain() { return domain; }
        public void setDomain(String domain) { this.domain = domain; }
        public boolean isKeepLocalCopy() { return keepLocalCopy; }
        public void setKeepLocalCopy(boolean keepLocalCopy) { this.keepLocalCopy = keepLocalCopy; }
    }
}


