package com.szz.common.storage;

import java.io.IOException;
import java.io.OutputStream;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

/**
 * 组合存储：根据配置选择具体实现。默认回落到本地实现。
 */
@Component
@Primary
public class CompositeFileStorage implements FileStorage
{
    private final StorageProperties storageProperties;
    private final LocalFileStorage localFileStorage;
    private final ObjectProvider<AliyunOssFileStorage> aliyunProvider;
    private final FileStorage localDelegate;

    public CompositeFileStorage(StorageProperties storageProperties,
                                LocalFileStorage localFileStorage,
                                ObjectProvider<AliyunOssFileStorage> aliyunProvider)
    {
        this.storageProperties = storageProperties;
        this.localFileStorage = localFileStorage;
        this.aliyunProvider = aliyunProvider;
        // 适配器：将 LocalFileStorage 适配为 FileStorage，避免产生多个 FileStorage Bean
        this.localDelegate = new FileStorage() {
            @Override
            public String store(String baseDir, org.springframework.web.multipart.MultipartFile file, String fileName) throws java.io.IOException {
                return CompositeFileStorage.this.localFileStorage.store(baseDir, file, fileName);
            }
            @Override
            public String store(String uploadDir, byte[] data, String fileName) throws java.io.IOException {
                return CompositeFileStorage.this.localFileStorage.store(uploadDir, data, fileName);
            }
            @Override
            public void write(String absoluteFilePath, java.io.OutputStream os) throws java.io.IOException {
                CompositeFileStorage.this.localFileStorage.write(absoluteFilePath, os);
            }
            @Override
            public byte[] readByResourcePath(String resourceUrl) throws java.io.IOException {
                return CompositeFileStorage.this.localFileStorage.readByResourcePath(resourceUrl);
            }
        };
    }

    private FileStorage choose()
    {
        String provider = storageProperties.getProvider();
        if ("local".equalsIgnoreCase(provider) || provider == null)
        {
            return localDelegate;
        }
        if ("aliyun".equalsIgnoreCase(provider))
        {
            AliyunOssFileStorage aliyun = aliyunProvider.getIfAvailable();
            if (aliyun != null)
            {
                return aliyun;
            }
        }
        // 其他未实现时回退本地
        return localDelegate;
    }

    @Override
    public String store(String baseDir, MultipartFile file, String fileName) throws IOException
    {
        return choose().store(baseDir, file, fileName);
    }

    @Override
    public String store(String uploadDir, byte[] data, String fileName) throws IOException
    {
        return choose().store(uploadDir, data, fileName);
    }

    @Override
    public void write(String absoluteFilePath, OutputStream os) throws IOException
    {
        choose().write(absoluteFilePath, os);
    }

    @Override
    public byte[] readByResourcePath(String resourceUrl) throws IOException
    {
        return choose().readByResourcePath(resourceUrl);
    }
}


