package com.szz.common.storage;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.OutputStream;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.model.PutObjectRequest;
import org.apache.commons.io.IOUtils;
import com.szz.common.config.RuoYiConfig;
import com.szz.common.constant.Constants;
import com.szz.common.utils.StringUtils;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

/**
 * 阿里云 OSS 存储实现（保持对外返回 /profile/**）。
 * 策略：
 * 1) 写入：将内容上传至 OSS，同时仍返回本地风格路径（/profile/**）。
 * 2) 读出：仍走本地读取（由上层控制器改造负责从 OSS 读回；此类只负责写入上传）。
 */
@Component
@ConditionalOnProperty(prefix = "ruoyi.storage", name = "provider", havingValue = "aliyun")
public class AliyunOssFileStorage implements FileStorage
{
    private final LocalFileStorage local;
    private final StorageProperties storageProperties;

    public AliyunOssFileStorage(LocalFileStorage local, StorageProperties storageProperties)
    {
        this.local = local;
        this.storageProperties = storageProperties;
    }

    private OSS buildClient()
    {
        StorageProperties.Aliyun cfg = storageProperties.getAliyun();
        return new OSSClientBuilder().build(cfg.getEndpoint(), cfg.getAccessKeyId(), cfg.getAccessKeySecret());
    }

    @Override
    public String store(String baseDir, MultipartFile file, String fileName) throws IOException
    {
        StorageProperties.Aliyun cfg = storageProperties.getAliyun();
        // 计算对象键：以 baseDir 相对于 profile 的目录作为前缀
        String dir = relativeDirFromProfile(baseDir);
        String key = (dir.isEmpty() ? "" : dir + "/") + fileName.replace("\\", "/");
        // 上传至 OSS
        OSS oss = null;
        try
        {
            oss = buildClient();
            PutObjectRequest put = new PutObjectRequest(cfg.getBucket(), key, file.getInputStream());
            oss.putObject(put);
        }
        finally
        {
            if (oss != null)
            {
                oss.shutdown();
            }
        }
        return buildPublicUrl(cfg, key);
    }

    @Override
    public String store(String uploadDir, byte[] data, String fileName) throws IOException
    {
        StorageProperties.Aliyun cfg = storageProperties.getAliyun();
        String dir = relativeDirFromProfile(uploadDir);
        String key = (dir.isEmpty() ? "" : dir + "/") + fileName.replace("\\", "/");
        OSS oss = null;
        try
        {
            oss = buildClient();
            PutObjectRequest put = new PutObjectRequest(cfg.getBucket(), key, new ByteArrayInputStream(data));
            oss.putObject(put);
        }
        finally
        {
            if (oss != null)
            {
                oss.shutdown();
            }
        }
        return buildPublicUrl(cfg, key);
    }

    @Override
    public void write(String absoluteFilePath, OutputStream os) throws IOException
    {
        // 保持与本地一致的读取方式；若需从 OSS 读回，应在上层控制器中实现
        local.write(absoluteFilePath, os);
    }

    @Override
    public byte[] readByResourcePath(String resourceUrl) throws IOException
    {
        return local.readByResourcePath(resourceUrl);
    }

    private String buildPublicUrl(StorageProperties.Aliyun cfg, String key)
    {
        if (StringUtils.isNotEmpty(cfg.getDomain()))
        {
            String domain = cfg.getDomain();
            if (domain.endsWith("/"))
            {
                domain = domain.substring(0, domain.length() - 1);
            }
            return domain + "/" + key;
        }
        return "https://" + cfg.getBucket() + "." + cfg.getEndpoint() + "/" + key;
    }

    private String relativeDirFromProfile(String baseDir)
    {
        String profile = RuoYiConfig.getProfile();
        String withSlash = profile.endsWith("/") ? profile : profile + "/";
        String dir = StringUtils.substringAfter(baseDir, withSlash);
        if (dir == null)
        {
            dir = StringUtils.substringAfter(baseDir, profile + "/");
        }
        return dir == null ? "" : dir.replace("\\", "/");
    }
}


