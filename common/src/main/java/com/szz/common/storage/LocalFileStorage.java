package com.szz.common.storage;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.nio.file.Paths;
import org.apache.commons.io.IOUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;
import com.szz.common.config.RuoYiConfig;
import com.szz.common.constant.Constants;
import com.szz.common.utils.StringUtils;

/**
 * 本地文件系统实现，保持现有行为与路径格式不变。
 * 作为底层实现供组合存储调用，不直接作为 FileStorage 对外暴露，避免产生多个 FileStorage Bean。
 */
@Component
public class LocalFileStorage {
    public String store(String baseDir, MultipartFile file, String fileName) throws IOException {
		File desc = getAbsoluteFile(baseDir, fileName);
		file.transferTo(Paths.get(desc.getAbsolutePath()));
		return getPathFileName(baseDir, fileName);
	}

    public String store(String uploadDir, byte[] data, String fileName) throws IOException {
		FileOutputStream fos = null;
		try {
			File file = getAbsoluteFile(uploadDir, fileName);
			fos = new FileOutputStream(file);
			fos.write(data);
		} finally {
			IOUtils.closeQuietly(fos);
		}
		return getPathFileName(uploadDir, fileName);
	}

	public void write(String absoluteFilePath, OutputStream os) throws IOException {
		FileInputStream fis = null;
		try {
			File file = new File(absoluteFilePath);
			if (!file.exists()) {
				throw new IOException("File not found: " + absoluteFilePath);
			}
			fis = new FileInputStream(file);
			byte[] buffer = new byte[1024];
			int length;
			while ((length = fis.read(buffer)) > 0) {
				os.write(buffer, 0, length);
			}
		} finally {
			IOUtils.closeQuietly(os);
			IOUtils.closeQuietly(fis);
		}
	}

	public byte[] readByResourcePath(String resourceUrl) throws IOException {
		String localPath = RuoYiConfig.getProfile();
		String downloadPath = localPath + StringUtils.substringAfter(resourceUrl, Constants.RESOURCE_PREFIX);
		FileInputStream fis = null;
		try {
			fis = new FileInputStream(downloadPath);
			return IOUtils.toByteArray(fis);
		} finally {
			IOUtils.closeQuietly(fis);
		}
	}

	private static File getAbsoluteFile(String uploadDir, String fileName) throws IOException {
		File desc = new File(uploadDir + File.separator + fileName);
		if (!desc.exists()) {
			if (!desc.getParentFile().exists()) {
				desc.getParentFile().mkdirs();
			}
		}
		return desc;
	}

	private static String getPathFileName(String uploadDir, String fileName) {
		int dirLastIndex = RuoYiConfig.getProfile().length() + 1;
		String currentDir = StringUtils.substring(uploadDir, dirLastIndex);
		return Constants.RESOURCE_PREFIX + "/" + currentDir + "/" + fileName;
	}
}


