package com.szz.common.storage;

import java.io.IOException;
import java.io.OutputStream;
import org.springframework.web.multipart.MultipartFile;

/**
 * 文件/对象存储抽象。
 * 默认实现为本地文件系统，保持现有对外接口与行为不变。
 */
public interface FileStorage {
	/**
	 * 将表单文件保存到指定基础目录下。
	 * @param baseDir 基础目录（如 RuoYiConfig.getUploadPath() 返回的路径）
	 * @param file 上传文件
	 * @param fileName 目标相对文件名（如 2024/08/13/xxx.png）
	 * @return 资源访问路径（通常为 /profile/**）
	 */
	String store(String baseDir, MultipartFile file, String fileName) throws IOException;

	/**
	 * 将字节数据保存到指定目录与文件名。
	 * @param uploadDir 基础目录
	 * @param data 字节数据
	 * @param fileName 目标相对文件名
	 * @return 资源访问路径（通常为 /profile/**）
	 */
	String store(String uploadDir, byte[] data, String fileName) throws IOException;

	/**
	 * 将绝对文件路径对应的内容写入输出流。
	 * 仅用于当前本地存储下载场景的透传。
	 */
	void write(String absoluteFilePath, OutputStream os) throws IOException;

	/**
	 * 根据资源访问路径（如 /profile/**）读取字节数据。
	 */
	byte[] readByResourcePath(String resourceUrl) throws IOException;
}


