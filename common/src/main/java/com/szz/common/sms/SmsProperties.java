package com.szz.common.sms;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 短信配置属性，读取 ruoyi.sms 下的配置。
 */
@Component
@ConfigurationProperties(prefix = "ruoyi.sms")
public class SmsProperties
{
    /** 提供方：none|tencent|aliyun */
    private String provider = "none";

    private final Tencent tencent = new Tencent();
    private final Aliyun aliyun = new Aliyun();

    public String getProvider()
    {
        return provider;
    }

    public void setProvider(String provider)
    {
        this.provider = provider;
    }

    public Tencent getTencent()
    {
        return tencent;
    }

    public Aliyun getAliyun()
    {
        return aliyun;
    }

    public static class Tencent
    {
        /** SecretId */
        private String secretId;
        /** SecretKey */
        private String secretKey;
        /** 应用ID（SmsSdkAppId） */
        private String sdkAppId;
        /** 签名 */
        private String signName;
        /** 区域，默认 ap-guangzhou */
        private String region = "ap-guangzhou";
        /** 端点，默认 sms.tencentcloudapi.com */
        private String endpoint = "sms.tencentcloudapi.com";

        public String getSecretId() { return secretId; }
        public void setSecretId(String secretId) { this.secretId = secretId; }
        public String getSecretKey() { return secretKey; }
        public void setSecretKey(String secretKey) { this.secretKey = secretKey; }
        public String getSdkAppId() { return sdkAppId; }
        public void setSdkAppId(String sdkAppId) { this.sdkAppId = sdkAppId; }
        public String getSignName() { return signName; }
        public void setSignName(String signName) { this.signName = signName; }
        public String getRegion() { return region; }
        public void setRegion(String region) { this.region = region; }
        public String getEndpoint() { return endpoint; }
        public void setEndpoint(String endpoint) { this.endpoint = endpoint; }
    }

    public static class Aliyun
    {
        /** AccessKeyId */
        private String accessKeyId;
        /** AccessKeySecret */
        private String accessKeySecret;
        /** 签名 */
        private String signName;
        /** 区域ID，默认 cn-hangzhou */
        private String regionId = "cn-hangzhou";
        /** 端点，默认 dysmsapi.aliyuncs.com */
        private String endpoint = "dysmsapi.aliyuncs.com";

        public String getAccessKeyId() { return accessKeyId; }
        public void setAccessKeyId(String accessKeyId) { this.accessKeyId = accessKeyId; }
        public String getAccessKeySecret() { return accessKeySecret; }
        public void setAccessKeySecret(String accessKeySecret) { this.accessKeySecret = accessKeySecret; }
        public String getSignName() { return signName; }
        public void setSignName(String signName) { this.signName = signName; }
        public String getRegionId() { return regionId; }
        public void setRegionId(String regionId) { this.regionId = regionId; }
        public String getEndpoint() { return endpoint; }
        public void setEndpoint(String endpoint) { this.endpoint = endpoint; }
    }
}


