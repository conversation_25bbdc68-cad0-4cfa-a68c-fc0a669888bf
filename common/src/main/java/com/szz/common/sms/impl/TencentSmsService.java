package com.szz.common.sms.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;
import com.szz.common.sms.SmsProperties;
import com.szz.common.sms.SmsService;

/**
 * 腾讯云短信实现（轻量HTTP版占位，避免引入SDK依赖）。
 * 实际使用时建议接入官方 SDK 并完善签名鉴权。
 */
@Component
@ConditionalOnProperty(prefix = "ruoyi.sms", name = "provider", havingValue = "tencent")
public class TencentSmsService implements SmsService
{
    private static final Logger log = LoggerFactory.getLogger(TencentSmsService.class);

    private final SmsProperties properties;

    public TencentSmsService(SmsProperties properties)
    {
        this.properties = properties;
    }

    @Override
    public boolean send(String phoneNumber, String templateId, Map<String, String> params)
    {
        // 占位实现：仅记录日志，返回true，确保编译与调用无阻塞
        // 如需真实发送，请引入腾讯云 SMS SDK 并按官方文档实现签名、模板参数与请求
        SmsProperties.Tencent cfg = properties.getTencent();
        List<String> paramList = new ArrayList<>(params.values());
        log.info("[TencentSMS] send -> phone={}, templateId={}, params={}, sdkAppId={}, signName={}, region={}",
                phoneNumber, templateId, paramList, cfg.getSdkAppId(), cfg.getSignName(), cfg.getRegion());
        return true;
    }
}


