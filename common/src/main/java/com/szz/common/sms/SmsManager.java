package com.szz.common.sms;

import java.util.Map;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.stereotype.Component;

/**
 * 对外暴露的短信发送门面，内部按配置选择具体实现。
 */
@Component
public class SmsManager
{
    private final SmsProperties smsProperties;
    private final ObjectProvider<SmsService> smsServiceProvider;

    public SmsManager(SmsProperties smsProperties, ObjectProvider<SmsService> smsServiceProvider)
    {
        this.smsProperties = smsProperties;
        this.smsServiceProvider = smsServiceProvider;
    }

    /**
     * 发送模板短信（无感知厂商）
     */
    public boolean send(String phoneNumber, String templateId, Map<String, String> params)
    {
        SmsService delegate = smsServiceProvider.getIfAvailable();
        if (delegate == null)
        {
            // 未启用任何厂商实现时，返回false
            return false;
        }
        return delegate.send(phoneNumber, templateId, params);
    }
}


