package com.szz.common.sms;

import java.util.LinkedHashMap;
import java.util.Map;
import com.szz.common.utils.spring.SpringUtils;

/**
 * 短信发送工具类：静态方法便于在任意位置调用。
 * 实际发送由 {@link SmsManager} 根据 yml 中 ruoyi.sms.provider 选择厂商实现。
 */
public final class SmsUtils
{
    private SmsUtils() {}

    /**
     * 使用 Map 传参（建议使用 LinkedHashMap 以保证参数顺序）。
     */
    public static boolean send(String phoneNumber, String templateId, Map<String, String> params)
    {
        SmsManager manager = SpringUtils.getBean(SmsManager.class);
        return manager != null && manager.send(phoneNumber, templateId, params);
    }

    /**
     * 以有序参数列表传参（内部按顺序构建参数 Map）。
     */
    public static boolean send(String phoneNumber, String templateId, String... orderedParams)
    {
        LinkedHashMap<String, String> map = new LinkedHashMap<>();
        if (orderedParams != null)
        {
            for (String value : orderedParams)
            {
                map.put(String.valueOf(map.size()), value);
            }
        }
        return send(phoneNumber, templateId, map);
    }
}


