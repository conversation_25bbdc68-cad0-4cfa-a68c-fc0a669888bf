package com.szz.common.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springdoc.core.models.GroupedOpenApi;

@Configuration
public class OpenApiConfig {

    @Bean
    public GroupedOpenApi businessGroup() {
        return GroupedOpenApi.builder()
                .group("01-业务接口")
                .pathsToMatch("/login")
                .build();
    }

    @Bean
    public GroupedOpenApi ruoyiBuiltinGroup() {
        return GroupedOpenApi.builder()
                .group("10-RuoYi-原生接口")
                .pathsToMatch(
                        "/login",
                        "/logout",
                        "/captchaImage",
                        "/system/**",
                        "/monitor/**",
                        "/tool/**",
                        "/common/**",
                        "/profile/**",
                        "/druid/**"
                )
                .build();
    }
}


