package com.szz.common.annotation;

import java.lang.annotation.*;

/**
 * 幂等控制注解：基于 Redis SET NX 实现请求在窗口期内只被受理一次。
 * 使用方式：
 * - keySpEL：从方法参数解析业务键，如 "#req.orderNo"；
 * - 或者在切面内读取 Header: Idempotency-Key 作为键；
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface Idempotent {
    /** 业务键的SpEL表达式，如 "#req.orderNo"；为空则走 Header: Idempotency-Key */
    String keySpEL() default "";
    /** 保护窗口期（秒） */
    int windowSeconds() default 60;
    /** 命中提示 */
    String message() default "请勿重复提交";
}


