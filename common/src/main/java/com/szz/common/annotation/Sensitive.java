package com.szz.common.annotation;

import com.szz.common.enums.SensitiveType;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 数据脱敏注解：用于在 JSON 序列化阶段对敏感字符串进行遮蔽。
 * 仅在返回给前端时生效，不影响数据库真实值。
 */
@Documented
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.FIELD, ElementType.METHOD})
public @interface Sensitive {

	/**
	 * 预置脱敏类型。
	 */
	SensitiveType type() default SensitiveType.CUSTOM;

	/**
	 * 自定义脱敏：保留前缀长度。
	 */
	int prefix() default 1;

	/**
	 * 自定义脱敏：保留后缀长度。
	 */
	int suffix() default 1;

	/**
	 * 脱敏时使用的掩码字符。
	 */
	char mask() default '*';
}


