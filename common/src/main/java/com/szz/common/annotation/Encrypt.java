package com.szz.common.annotation;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 标记控制器接口需要进行入参解密 / 出参加密。
 * 仅对标注了本注解的方法生效。
 */
@Documented
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.METHOD})
public @interface Encrypt {
    /**
     * 是否启用当前方法的加解密（默认启用）。
     */
    boolean value() default true;
}

