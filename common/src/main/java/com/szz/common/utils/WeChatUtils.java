package com.szz.common.utils;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.szz.common.exception.UtilException;
import com.wechat.pay.java.core.util.PemUtil;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.SerializationException;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.io.support.ResourcePatternResolver;
import org.springframework.stereotype.Component;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.security.KeyFactory;
import java.security.NoSuchAlgorithmException;
import java.security.PrivateKey;
import java.security.Signature;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.PKCS8EncodedKeySpec;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

/**
 * @Author: cc
 * @Date: 2024/4/18
 * @Description: 微信服务工具类
 */
@Component
public class WeChatUtils {

  /**
   * 发起 HTTP 请求
   *
   * @param apiUrl      请求的API地址
   * @param method      请求方法，例如"POST"或"GET"
   * @param contentType 请求头的Content-Type
   * @param requestData 请求的数据，以Map形式传入
   * @return 服务器响应的数据，以Map形式返回
   * @throws IOException 当发生IO异常时抛出
   */
  private static Map<String, Object> httpRequest(String apiUrl, String method, String contentType, Map<String, Object> requestData)  {
    try {
      // 初始化 ObjectMapper
      ObjectMapper objectMapper = new ObjectMapper();

      // 如果有请求数据，转换成 JSON 格式
      String requestBody = null;
      if (requestData != null) {
        requestBody = objectMapper.writeValueAsString(requestData);
      }

      // 发起 HTTP 请求
      URL url = new URL(apiUrl);
      HttpURLConnection con = (HttpURLConnection) url.openConnection();
      con.setRequestMethod(method); // 设置请求方法
      con.setRequestProperty("Content-Type", contentType); // 设置请求头的 Content-Type
      con.setDoOutput(true);

      // 如果有请求数据，将其写入请求体
      if (requestBody != null) {
        try (OutputStream os = con.getOutputStream()) {
          byte[] input = requestBody.getBytes("utf-8");
          os.write(input, 0, input.length);
        }
      }

      // 读取服务器响应
      try (BufferedReader in = new BufferedReader(new InputStreamReader(con.getInputStream()))) {
        StringBuilder response = new StringBuilder();
        String inputLine;
        while ((inputLine = in.readLine()) != null) {
          response.append(inputLine);
        }

        // 解析 JSON 数据
        Map<String, Object> responseData = objectMapper.readValue(response.toString(), Map.class);
        return responseData;
      }
    }catch(Exception e){
      e.printStackTrace();
      throw new UtilException(e.getMessage());
    }
  }

  /**
   * 获取 AccessToken
   *
   * @param appId     应用ID
   * @param appSecret 应用密钥
   * @return 服务器响应的数据，以Map形式返回
   * @throws IOException 当发生IO异常时抛出
   */
  public static Map<String, Object> getAccessToken(String appId, String appSecret) {
    String apiUrl = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=" + appId + "&secret=" + appSecret;
    return httpRequest(apiUrl, "POST", "application/json", null);
  }

  /**
   * 获取手机号
   *
   * @param appId     应用ID
   * @param appSecret 应用密钥
   * @param code      用户授权码
   * @return 用户手机号
   * @throws Exception 当获取手机号失败时抛出异常
   */
  public static String getPhoneNumber(String appId, String appSecret, String code) {
    // 获取 AccessToken
    Map<String, Object> body = getAccessToken(appId, appSecret);
    String access_token = body.get("access_token").toString();
    String apiUrl = "https://api.weixin.qq.com/wxa/business/getuserphonenumber?access_token=" + access_token;

    // 构建请求体
    Map<String, Object> data = new HashMap<>();
    data.put("code", code);

    // 发起请求
    Map<String, Object> responseData = httpRequest(apiUrl, "POST", "application/json", data);
    if (responseData.get("errcode").equals(0)) {
      Map<String, Object> phoneInfo = (HashMap) responseData.get("phone_info");
      return phoneInfo.get("purePhoneNumber").toString();
    } else {
      throw new UtilException(responseData.get("errmsg").toString());
    }
  }

  /**
   * 获取 openid
   * @param appId
   * @param appSecret
   * @param code
   * @return
   */
  public static String getOpenid(String appId, String appSecret, String code){
    String apiUrl =new StringBuilder().append("https://api.weixin.qq.com/sns/jscode2session?appid=").append(appId).append("&secret=").append(appSecret).append("&js_code=").append(code).toString();
    Map<String, Object> responseData = httpRequest(apiUrl, "GET", "application/json", null);
    String openid = responseData.get("openid").toString();
    if(openid==null){
      throw new UtilException("客户端异常参数！");
    }
    return openid;
  }

  /**
   * 生成签名
   */
  public static Map<String, Object> genSign(String appId,String prepayId) {
    try {
      String timestamp = String.valueOf(System.currentTimeMillis());
      String nonceStr = genNonceStr(32);
      StringBuilder sb = new StringBuilder();
      // 应用id
      sb.append(appId).append("\n");
      // 支付签名时间戳
      sb.append(timestamp).append("\n");
      // 随机字符串
      sb.append(nonceStr).append("\n");
      // 预支付交易会话ID
      sb.append("prepay_id=").append(prepayId).append("\n");
      // 签名
      Signature sign = Signature.getInstance("SHA256withRSA");
      // 获取商户私钥并进行签名
      PrivateKey privateKey = getPrivateKey();
      sign.initSign(privateKey);
      sign.update(sb.toString().getBytes("utf-8"));
      String paySign = Base64.getEncoder().encodeToString(sign.sign());

      // 将签名时数据和签名一起返回前端用于前端吊起支付
      Map<String, Object> map = new HashMap<>();
      map.put("appId", appId); // 小程序id
      map.put("timeStamp", timestamp); // 时间戳
      map.put("nonceStr", nonceStr); // 随机字符串
      map.put("package", "prepay_id=" + prepayId); // 预支付交易会话ID
      map.put("signType", "RSA"); // 签名方式
      map.put("paySign", paySign); // 签名
      return map;
    }catch (Exception e){
      throw new SerializationException(e.getMessage());
    }
  }
  public static PrivateKey getPrivateKey() throws IOException {
    ResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
    Resource[] resources = resolver.getResources("cert/apiclient_key.pem");
    Resource resource = resources[0];
    //获得文件流，在jar文件中，不能直接通过文件资源路径拿到文件，但可以在jar包中拿到文件流
    InputStream inputStream = resource.getInputStream();
//    ClassPathResource resource = new ClassPathResource("cert/apiclient_key.pem");
//    // 读取文件内容
//    StringBuilder contentBuilder = new StringBuilder();
//    try (InputStream inputStream = resource.getInputStream();
//         BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream))) {
//      String line;
//      while ((line = reader.readLine()) != null) {
//        contentBuilder.append(line).append("\n");
//      }
//    }



    try {
      String privateKey = "MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQC9CnAo9KJMpto8d7jiIxc2kBt3fnqSwc/GpbhCpfFxdIHbSWWEyuGsbTYleBKkEl+sM3q9CSg2fudq2ZGRPzSAVMs8FRDvv8rGvzgSK+751IRE6lHp5EThQn5XyoDx/ypmu9liZDMJ0uG99iXX4C+YffbOmLpHqF5h9DsksU0JsFSZA1j6goO/6XVwQ+rcK51Q+ua/kyOejkjRvjhN5DI2a1LP7NuFlX281yYev9fjLJFYU36GNT8l1nNXEHqFqo1BpLA67jMtMn2JneZYrv8VVP7M8LDGvYPp7+Daywe4neHPOeQgXrJzHGm5anwVyglUW+Fs2yrjZ38oLGypLCU5AgMBAAECggEAMsUX5Ff3MsDpAa5hsBIejN/dEJWeU8XLiJ0/RF8O3hqdOYiXxDc8rrNSBHQgBYKNwVC+GQDZlPE2M6MlDi48am7belJqDwmiawg2BeNYti4WK/AGedt9iEX0ap2PTe7qs1amBl/EuVGyoPL3rOefwMOrwkaQWOhmGMagD7Wya/RBmDndJtUP7GA9vNN6/v8w2M2KBg9+o2ZMYWoAc+Hpe5hFUsy561cfp/JnS2px82f4VbSUykjgnemHFkEPwz+2E6JYD1DxF5bokZtDsykz1DwamTJ3HBhkX+QuSoBhXsp/I6aey18eIMVebT5+EdkkKFvnT2MSEnyifM3/eiJN9QKBgQD1Lcn7WBL0QgNKoe9I0VuAZk5vhlcSfjQ893gRAERlNfz5I4HXCWFIncVAbMpcQ3hWCHsQNGYGPXKO8PiN2HOaLZIjTba1we51dsJuXZp4KZtOZCtPjGvK9p7U82xNs0i9CxVvOlg7N1Y2SpOwn5HZT+mth1utEvXPEAwAHX8KNwKBgQDFYlwXxDwA3F0y5E3qe2osA5yzrUdX5u4zefQ2BilBusBW7G2gBkLLut8AiFm703R4yD9BFnUp0/tjmEF2txVj7NboTQloFr81K0e4SnsRGls9HA/M2ZNbBGJHe7NvuXDO+tVt36Q1o4r8ompcKU3uhABpDYipleCMIi858WPUDwKBgQCARDo9IjCYVMjADMPQhqamH9+8/L0k0Z2mn5jS3B7sDDiSZn8Q7rlUciW1tLSRXELiT4GCUsdlUFRUb8C5BJpF+L1RduC1agXEsiK59oJPhwTXGBSIg2AJSMiDg+4PIFJXhCrsi8uI/0Q9Fvr5FiIJXINV41CU1Sx/7Vc54DSMNQKBgQCmMnG0mUikuOfIiC+K9iupr8jsCUT/ILUEpvUX4AwxChwVlLGhgmQPwuMuQaC85Ak4AfQwy8YPWCxd6b+FRBdAshUqOGNpP230SOL0q9H/zp9wTwaGx9SpWIGgCupFcnEn9lBrWVmcyH2oxuCjhSvXX1IxInIlyMrvc4EsWrJhxQKBgDua2+UadmIXAqTSX3trM6lY71qLuieSltL/41c3KERvm2yEbB0XXzMH2QtFtpVX5h2hmGl7EGaXuDb1+jDvUe+xWLqmyEthg1DQwhMqy/KlmtIcXqp/tU6UzWXoby3p8xjbsLWKO+Awo06KGs8+JQNjwH+cNz4TeBClc+1wWYo4";

      System.out.println("privateKey :"+privateKey);
      KeyFactory kf = KeyFactory.getInstance("RSA");
      return kf.generatePrivate(
          new PKCS8EncodedKeySpec(Base64.getDecoder().decode(privateKey)));
    } catch (NoSuchAlgorithmException e) {
      throw new RuntimeException("当前Java环境不支持RSA", e);
    } catch (InvalidKeySpecException e) {
      throw new RuntimeException("无效的密钥格式");
    }
  }

  /**
   * 生成随机字符串
   */
  public static String genNonceStr(int size) {
    String abc = "qwertyuiopasdfghjklzxcvbnmQWERTYUIOPASDFGHJKLZXCVBNM1234567890";
    return  RandomStringUtils.random(size, abc);
  }

}
