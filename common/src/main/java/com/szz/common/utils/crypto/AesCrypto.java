package com.szz.common.utils.crypto;

import org.bouncycastle.jce.provider.BouncyCastleProvider;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.Security;
import java.util.Base64;

/**
 * 简单 SM4/CBC/PKCS7Padding 加解密工具。
 * 说明：
 * 1) 出于最小改动原则，保留原类名 AesCrypto，避免大范围重构；实现已切换为 SM4。
 * 2) 生产环境请通过配置文件安全管理密钥与向量。
 */
public class AesCrypto {
	// 注意: 生产环境请通过配置文件安全管理密钥与向量
	private static volatile String KEY; // 16字节（SM4 密钥固定 128bit）
	private static volatile String IV;  // 16字节

	/**
	 * 由配置类在应用启动时注入密钥与偏移量，并注册 BouncyCastle Provider。
	 * @param key 16 字节密钥
	 * @param iv  16 字节偏移量
	 */
	public static void configure(String key, String iv) {
		// 注册 BouncyCastle Provider（幂等）
		if (Security.getProvider(BouncyCastleProvider.PROVIDER_NAME) == null) {
			Security.addProvider(new BouncyCastleProvider());
		}
		KEY = key;
		IV = iv;
	}

	private static SecretKeySpec requireKeySpec() {
		if (KEY == null || KEY.isBlank()) {
			throw new IllegalStateException("SM4 key not configured. Please set encrypt.aes-key in application.yml");
		}
		return new SecretKeySpec(KEY.getBytes(StandardCharsets.UTF_8), "SM4");
	}

	private static IvParameterSpec requireIvSpec() {
		if (IV == null || IV.isBlank()) {
			throw new IllegalStateException("SM4 IV not configured. Please set encrypt.aes-iv in application.yml");
		}
		return new IvParameterSpec(IV.getBytes(StandardCharsets.UTF_8));
	}

    /**
     * 使用 SM4/CBC/PKCS7Padding 对明文进行加密，并返回 Base64 字符串。
     * @param plain 明文字符串
     * @return Base64 编码的密文
     */
    public static String encrypt(String plain) {
        if (plain == null) return null;
		try {
			Cipher cipher = Cipher.getInstance("SM4/CBC/PKCS7Padding", BouncyCastleProvider.PROVIDER_NAME);
			SecretKeySpec keySpec = requireKeySpec();
			IvParameterSpec ivSpec = requireIvSpec();
            cipher.init(Cipher.ENCRYPT_MODE, keySpec, ivSpec);
            byte[] enc = cipher.doFinal(plain.getBytes(StandardCharsets.UTF_8));
            return Base64.getEncoder().encodeToString(enc);
        } catch (Exception e) {
            throw new RuntimeException("SM4 encrypt error", e);
        }
    }

    /**
     * 使用 SM4/CBC/PKCS7Padding 对 Base64 密文进行解密，返回明文字符串。
     * @param cipherText Base64 编码的密文
     * @return 明文字符串
     */
    public static String decrypt(String cipherText) {
        if (cipherText == null) return null;
		try {
			Cipher cipher = Cipher.getInstance("SM4/CBC/PKCS7Padding", BouncyCastleProvider.PROVIDER_NAME);
			SecretKeySpec keySpec = requireKeySpec();
			IvParameterSpec ivSpec = requireIvSpec();
            cipher.init(Cipher.DECRYPT_MODE, keySpec, ivSpec);
            byte[] dec = cipher.doFinal(Base64.getDecoder().decode(cipherText));
            return new String(dec, StandardCharsets.UTF_8);
        } catch (Exception e) {
            throw new RuntimeException("SM4 decrypt error", e);
        }
    }
}

