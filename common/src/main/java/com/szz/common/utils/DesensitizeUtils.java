package com.szz.common.utils;

import com.szz.common.enums.SensitiveType;

public final class DesensitizeUtils {

	private DesensitizeUtils() {}

	public static String mask(String raw, SensitiveType type, int prefix, int suffix, char mask) {
		if (raw == null || raw.isEmpty()) {
			return raw;
		}
		return switch (type) {
			case CHINESE_NAME -> maskChineseName(raw, mask);
			case MOBILE -> maskMobile(raw, mask);
			case TEL -> maskTel(raw, mask);
			case ID_CARD -> maskIdCard(raw, mask);
			case EMAIL -> maskEmail(raw, mask);
			case BANK_CARD -> maskBankCard(raw, mask);
			case ADDRESS -> maskAddress(raw, mask);
			case CUSTOM -> maskCustom(raw, prefix, suffix, mask);
		};
	}

	private static String maskCustom(String raw, int prefix, int suffix, char mask) {
		if (raw.length() <= prefix + suffix) {
			return repeat(mask, Math.max(1, raw.length()));
		}
		String start = raw.substring(0, Math.max(0, prefix));
		String end = raw.substring(raw.length() - Math.max(0, suffix));
		int maskLen = raw.length() - start.length() - end.length();
		return start + repeat(mask, maskLen) + end;
	}

	private static String maskChineseName(String name, char mask) {
		if (name.length() <= 1) return String.valueOf(mask);
		return name.charAt(0) + repeat(mask, name.length() - 1);
	}

	private static String maskMobile(String mobile, char mask) {
		String digits = mobile.replaceAll("\\D", "");
		if (digits.length() < 7) {
			return maskCustom(mobile, 3, 2, mask);
		}
		return digits.substring(0, 3) + repeat(mask, Math.max(4, digits.length() - 7)) + digits.substring(digits.length() - 4);
	}

	private static String maskTel(String tel, char mask) {
		return maskCustom(tel, 0, 4, mask);
	}

	private static String maskIdCard(String id, char mask) {
		String digits = id.replaceAll("\\s", "");
		if (digits.length() <= 8) return repeat(mask, digits.length());
		return digits.substring(0, 4) + repeat(mask, Math.max(6, digits.length() - 8)) + digits.substring(digits.length() - 4);
	}

	private static String maskEmail(String email, char mask) {
		int at = email.indexOf('@');
		if (at <= 1) return repeat(mask, Math.max(1, email.length()));
		String name = email.substring(0, at);
		String domain = email.substring(at);
		return name.charAt(0) + repeat(mask, Math.max(2, name.length() - 1)) + domain;
	}

	private static String maskBankCard(String card, char mask) {
		String digits = card.replaceAll("\\s", "");
		if (digits.length() <= 8) return repeat(mask, digits.length());
		return digits.substring(0, 4) + repeat(mask, Math.max(6, digits.length() - 8)) + digits.substring(digits.length() - 4);
	}

	private static String maskAddress(String address, char mask) {
		return maskCustom(address, Math.min(6, address.length()), 0, mask);
	}

	private static String repeat(char c, int len) {
		return String.valueOf(c).repeat(Math.max(0, len));
	}
}


